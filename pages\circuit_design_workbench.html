<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Circuit Design Workbench - BioMed Lab Pro</title>
    <link rel="stylesheet" href="../css/main.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fbiomedlab3601back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.6"></script>
</head>
<body class="bg-background text-text-primary font-inter min-h-screen">
    <!-- Header Command Center -->
    <header class="bg-surface border-b border-slate-700 sticky top-0 z-50 backdrop-blur-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo and Brand -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-heartbeat text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gradient-primary">BioMed Lab Pro</h1>
                            <p class="text-xs text-text-secondary">Educational Technology Platform</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="dashboard_hub.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                    </a>
                    <a href="module_explorer.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-compass mr-2"></i>Explore
                    </a>
                    <a href="simulation_laboratory.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-flask mr-2"></i>Laboratory
                    </a>
                    <a href="circuit_design_workbench.html" class="text-primary-400 font-semibold border-b-2 border-primary-400 pb-1">
                        <i class="fas fa-microchip mr-2"></i>Workbench
                    </a>
                </nav>

                <!-- Bilingual Toggle and User Actions -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center bg-surface-light rounded-lg p-1">
                        <button id="langEn" class="px-3 py-1 rounded-md text-sm font-medium bg-primary text-white transition-all duration-300">
                            EN
                        </button>
                        <button id="langAr" class="px-3 py-1 rounded-md text-sm font-medium text-text-secondary hover:text-white transition-all duration-300">
                            عربي
                        </button>
                    </div>
                    <div class="w-8 h-8 bg-gradient-to-br from-accent-400 to-accent-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <button class="md:hidden text-text-secondary hover:text-white">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Workbench Interface -->
    <main class="flex h-screen overflow-hidden">
        <!-- Component Library Sidebar -->
        <aside class="w-80 bg-surface border-r border-slate-700 flex flex-col">
            <!-- Sidebar Header -->
            <div class="p-6 border-b border-slate-700">
                <h2 class="text-xl font-semibold text-white mb-2">Component Library</h2>
                <p class="text-sm text-text-secondary">Drag components to canvas</p>
            </div>

            <!-- Search and Filter -->
            <div class="p-4 border-b border-slate-700">
                <div class="relative mb-4">
                    <input type="text" id="componentSearch" placeholder="Search components..." class="input-field w-full pl-10 pr-4 py-2 text-sm" />
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary"></i>
                </div>
                <div class="flex space-x-2">
                    <button class="filter-btn active" data-category="all">All</button>
                    <button class="filter-btn" data-category="amplifiers">Amps</button>
                    <button class="filter-btn" data-category="filters">Filters</button>
                    <button class="filter-btn" data-category="sensors">Sensors</button>
                </div>
            </div>

            <!-- Component Categories -->
            <div class="flex-1 overflow-y-auto p-4">
                <!-- Operational Amplifiers -->
                <div class="component-category mb-6" data-category="amplifiers">
                    <h3 class="text-sm font-semibold text-accent-400 mb-3 flex items-center">
                        <i class="fas fa-microchip mr-2"></i>Operational Amplifiers
                    </h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="component-item" draggable="true" data-component="op-amp-741">
                            <div class="w-full h-16 bg-surface-light rounded-lg flex items-center justify-center mb-2 border border-slate-600 hover:border-primary-400 transition-colors cursor-grab">
                                <i class="fas fa-microchip text-primary-400 text-lg"></i>
                            </div>
                            <p class="text-xs text-center text-white font-medium">LM741</p>
                            <p class="text-xs text-center text-text-secondary">Op-Amp</p>
                        </div>
                        <div class="component-item" draggable="true" data-component="instrumentation-amp">
                            <div class="w-full h-16 bg-surface-light rounded-lg flex items-center justify-center mb-2 border border-slate-600 hover:border-primary-400 transition-colors cursor-grab">
                                <i class="fas fa-microchip text-secondary text-lg"></i>
                            </div>
                            <p class="text-xs text-center text-white font-medium">INA128</p>
                            <p class="text-xs text-center text-text-secondary">Inst. Amp</p>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="component-category mb-6" data-category="filters">
                    <h3 class="text-sm font-semibold text-accent-400 mb-3 flex items-center">
                        <i class="fas fa-wave-square mr-2"></i>Filters & Signal Conditioning
                    </h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="component-item" draggable="true" data-component="low-pass-filter">
                            <div class="w-full h-16 bg-surface-light rounded-lg flex items-center justify-center mb-2 border border-slate-600 hover:border-accent-400 transition-colors cursor-grab">
                                <i class="fas fa-wave-square text-accent-400 text-lg"></i>
                            </div>
                            <p class="text-xs text-center text-white font-medium">LPF</p>
                            <p class="text-xs text-center text-text-secondary">Low Pass</p>
                        </div>
                        <div class="component-item" draggable="true" data-component="high-pass-filter">
                            <div class="w-full h-16 bg-surface-light rounded-lg flex items-center justify-center mb-2 border border-slate-600 hover:border-accent-400 transition-colors cursor-grab">
                                <i class="fas fa-wave-square text-accent-400 text-lg"></i>
                            </div>
                            <p class="text-xs text-center text-white font-medium">HPF</p>
                            <p class="text-xs text-center text-text-secondary">High Pass</p>
                        </div>
                        <div class="component-item" draggable="true" data-component="notch-filter">
                            <div class="w-full h-16 bg-surface-light rounded-lg flex items-center justify-center mb-2 border border-slate-600 hover:border-accent-400 transition-colors cursor-grab">
                                <i class="fas fa-wave-square text-error-500 text-lg"></i>
                            </div>
                            <p class="text-xs text-center text-white font-medium">Notch</p>
                            <p class="text-xs text-center text-text-secondary">60Hz Filter</p>
                        </div>
                    </div>
                </div>

                <!-- Biomedical Sensors -->
                <div class="component-category mb-6" data-category="sensors">
                    <h3 class="text-sm font-semibold text-accent-400 mb-3 flex items-center">
                        <i class="fas fa-heartbeat mr-2"></i>Biomedical Sensors
                    </h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="component-item" draggable="true" data-component="ecg-electrode">
                            <div class="w-full h-16 bg-surface-light rounded-lg flex items-center justify-center mb-2 border border-slate-600 hover:border-secondary transition-colors cursor-grab">
                                <i class="fas fa-heartbeat text-secondary text-lg"></i>
                            </div>
                            <p class="text-xs text-center text-white font-medium">ECG</p>
                            <p class="text-xs text-center text-text-secondary">Electrode</p>
                        </div>
                        <div class="component-item" draggable="true" data-component="eeg-electrode">
                            <div class="w-full h-16 bg-surface-light rounded-lg flex items-center justify-center mb-2 border border-slate-600 hover:border-primary-400 transition-colors cursor-grab">
                                <i class="fas fa-brain text-primary-400 text-lg"></i>
                            </div>
                            <p class="text-xs text-center text-white font-medium">EEG</p>
                            <p class="text-xs text-center text-text-secondary">Electrode</p>
                        </div>
                    </div>
                </div>

                <!-- Passive Components -->
                <div class="component-category mb-6" data-category="passive">
                    <h3 class="text-sm font-semibold text-accent-400 mb-3 flex items-center">
                        <i class="fas fa-bolt mr-2"></i>Passive Components
                    </h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="component-item" draggable="true" data-component="resistor">
                            <div class="w-full h-16 bg-surface-light rounded-lg flex items-center justify-center mb-2 border border-slate-600 hover:border-accent-400 transition-colors cursor-grab">
                                <div class="w-8 h-2 bg-accent-400 rounded-sm"></div>
                            </div>
                            <p class="text-xs text-center text-white font-medium">Resistor</p>
                            <p class="text-xs text-center text-text-secondary">1kΩ - 1MΩ</p>
                        </div>
                        <div class="component-item" draggable="true" data-component="capacitor">
                            <div class="w-full h-16 bg-surface-light rounded-lg flex items-center justify-center mb-2 border border-slate-600 hover:border-accent-400 transition-colors cursor-grab">
                                <div class="flex items-center space-x-1">
                                    <div class="w-1 h-6 bg-accent-400"></div>
                                    <div class="w-1 h-6 bg-accent-400"></div>
                                </div>
                            </div>
                            <p class="text-xs text-center text-white font-medium">Capacitor</p>
                            <p class="text-xs text-center text-text-secondary">1nF - 100μF</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Template Circuits -->
            <div class="p-4 border-t border-slate-700">
                <h3 class="text-sm font-semibold text-white mb-3">Circuit Templates</h3>
                <div class="space-y-2">
                    <button class="template-btn w-full" data-template="ecg-amplifier">
                        <i class="fas fa-heartbeat mr-2"></i>ECG Amplifier
                    </button>
                    <button class="template-btn w-full" data-template="eeg-filter">
                        <i class="fas fa-brain mr-2"></i>EEG Filter Chain
                    </button>
                </div>
            </div>
        </aside>

        <!-- Main Design Canvas Area -->
        <div class="flex-1 flex flex-col">
            <!-- Canvas Toolbar -->
            <div class="bg-surface border-b border-slate-700 p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h2 class="text-lg font-semibold text-white">Circuit Design Canvas</h2>
                        <div class="flex items-center space-x-2">
                            <button id="zoomOut" class="p-2 bg-surface-light rounded-lg hover:bg-slate-600 transition-colors">
                                <i class="fas fa-search-minus text-text-secondary"></i>
                            </button>
                            <span id="zoomLevel" class="text-sm text-text-secondary px-2">100%</span>
                            <button id="zoomIn" class="p-2 bg-surface-light rounded-lg hover:bg-slate-600 transition-colors">
                                <i class="fas fa-search-plus text-text-secondary"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button id="clearCanvas" class="btn-secondary text-sm px-4 py-2">
                            <i class="fas fa-trash mr-2"></i>Clear
                        </button>
                        <button id="saveCircuit" class="btn-accent text-sm px-4 py-2">
                            <i class="fas fa-save mr-2"></i>Save
                        </button>
                        <button id="analyzeCircuit" class="btn-primary text-sm px-4 py-2">
                            <i class="fas fa-chart-line mr-2"></i>Analyze
                        </button>
                    </div>
                </div>
            </div>

            <!-- Canvas Container -->
            <div class="flex-1 relative overflow-hidden">
                <!-- Grid Canvas -->
                <div id="designCanvas" class="w-full h-full bg-background relative cursor-crosshair">
                    <!-- Grid Pattern -->
                    <svg class="absolute inset-0 w-full h-full pointer-events-none" style="z-index: 1;">
                        <defs>
                            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#334155" stroke-width="0.5" opacity="0.3"/>
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#grid)"/>
                    </svg>
                    
                    <!-- Component Drop Zone -->
                    <div id="componentContainer" class="absolute inset-0" style="z-index: 2;">
                        <!-- Components will be dynamically added here -->
                    </div>
                    
                    <!-- Connection Wires -->
                    <svg id="wireContainer" class="absolute inset-0 w-full h-full pointer-events-none" style="z-index: 3;">
                        <!-- Wires will be drawn here -->
                    </svg>
                </div>

                <!-- Canvas Status Bar -->
                <div class="absolute bottom-4 left-4 bg-surface/90 backdrop-blur-sm rounded-lg p-3 border border-slate-700">
                    <div class="flex items-center space-x-4 text-sm">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-secondary rounded-full"></div>
                            <span class="text-text-secondary">Components: <span id="componentCount" class="text-white">0</span></span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                            <span class="text-text-secondary">Connections: <span id="connectionCount" class="text-white">0</span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analysis Panel -->
        <aside id="analysisPanel" class="w-96 bg-surface border-l border-slate-700 transform translate-x-full transition-transform duration-300 ease-in-out">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold text-white">Circuit Analysis</h3>
                    <button id="closeAnalysis" class="text-text-secondary hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Analysis Tabs -->
                <div class="flex space-x-1 mb-6 bg-surface-light rounded-lg p-1">
                    <button class="analysis-tab active" data-tab="performance">Performance</button>
                    <button class="analysis-tab" data-tab="frequency">Frequency</button>
                    <button class="analysis-tab" data-tab="noise">Noise</button>
                </div>

                <!-- Performance Analysis -->
                <div id="performanceTab" class="analysis-content">
                    <div class="space-y-4">
                        <div class="card">
                            <h4 class="font-semibold text-white mb-3">Gain Analysis</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-text-secondary">DC Gain:</span>
                                    <span class="text-white font-mono" id="dcGain">--</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-text-secondary">Bandwidth:</span>
                                    <span class="text-white font-mono" id="bandwidth">--</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-text-secondary">Input Impedance:</span>
                                    <span class="text-white font-mono" id="inputImpedance">--</span>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h4 class="font-semibold text-white mb-3">Biomedical Specs</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-text-secondary">CMRR:</span>
                                    <span class="text-secondary font-mono" id="cmrr">--</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-text-secondary">SNR:</span>
                                    <span class="text-secondary font-mono" id="snr">--</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Frequency Analysis -->
                <div id="frequencyTab" class="analysis-content hidden">
                    <div class="card mb-4">
                        <h4 class="font-semibold text-white mb-3">Bode Plot</h4>
                        <div class="w-full h-48 bg-surface-light rounded-lg flex items-center justify-center">
                            <canvas id="bodeChart" width="300" height="180"></canvas>
                        </div>
                    </div>
                    <div class="card">
                        <h4 class="font-semibold text-white mb-3">Transfer Function</h4>
                        <div class="bg-surface-light rounded-lg p-3">
                            <code class="text-accent-400 text-sm" id="transferFunction">H(s) = --</code>
                        </div>
                    </div>
                </div>

                <!-- Noise Analysis -->
                <div id="noiseTab" class="analysis-content hidden">
                    <div class="space-y-4">
                        <div class="card">
                            <h4 class="font-semibold text-white mb-3">Noise Performance</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-text-secondary">Input Noise:</span>
                                    <span class="text-white font-mono" id="inputNoise">--</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-text-secondary">Output Noise:</span>
                                    <span class="text-white font-mono" id="outputNoise">--</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-text-secondary">Noise Figure:</span>
                                    <span class="text-white font-mono" id="noiseFigure">--</span>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <h4 class="font-semibold text-white mb-3">Noise Spectrum</h4>
                            <div class="w-full h-32 bg-surface-light rounded-lg flex items-center justify-center">
                                <canvas id="noiseChart" width="300" height="120"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
    </main>

    <!-- Component Properties Modal -->
    <div id="componentModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden flex items-center justify-center">
        <div class="bg-surface rounded-2xl p-6 max-w-md w-full mx-4 border border-slate-700">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white" id="modalTitle">Component Properties</h3>
                <button id="closeModal" class="text-text-secondary hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modalContent" class="space-y-4">
                <!-- Dynamic content will be inserted here -->
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button id="cancelModal" class="px-4 py-2 text-text-secondary hover:text-white transition-colors">Cancel</button>
                <button id="saveModal" class="btn-primary text-sm px-4 py-2">Save Changes</button>
            </div>
        </div>
    </div>

    <!-- Floating Help Button -->
    <div class="fixed bottom-6 right-6 z-40">
        <button id="helpButton" class="w-14 h-14 bg-gradient-to-br from-accent-500 to-accent-600 rounded-full shadow-dramatic hover:shadow-glow-accent transition-all duration-300 flex items-center justify-center group">
            <i class="fas fa-question text-white text-lg group-hover:scale-110 transition-transform duration-300"></i>
        </button>
    </div>

    <!-- Custom Styles -->
    <style>
        .filter-btn {
            @apply px-3 py-1 text-xs rounded-md bg-surface-light text-text-secondary hover:text-white transition-all duration-300;
        }
        .filter-btn.active {
            @apply bg-primary text-white;
        }
        .template-btn {
            @apply p-3 bg-surface-light rounded-lg text-sm text-text-secondary hover:text-white hover:bg-slate-600 transition-all duration-300 text-left;
        }
        .analysis-tab {
            @apply flex-1 px-3 py-2 text-sm rounded-md text-text-secondary hover:text-white transition-all duration-300;
        }
        .analysis-tab.active {
            @apply bg-primary text-white;
        }
        .component-item:hover {
            @apply transform scale-105 transition-transform duration-200;
        }
        .dragging {
            @apply opacity-50 transform rotate-3;
        }
        .drop-zone {
            @apply border-2 border-dashed border-primary-400 bg-primary-900/20;
        }
    </style>

    <!-- JavaScript for Circuit Design Functionality -->
    <script>
        // Global variables
        let currentZoom = 1;
        let componentCounter = 0;
        let connectionCounter = 0;
        let selectedComponent = null;
        let isConnecting = false;
        let currentConnection = null;

        // Language Toggle Functionality
        const langEn = document.getElementById('langEn');
        const langAr = document.getElementById('langAr');
        const htmlElement = document.documentElement;

        langEn.addEventListener('click', () => {
            htmlElement.setAttribute('lang', 'en');
            htmlElement.setAttribute('dir', 'ltr');
            langEn.classList.add('bg-primary', 'text-white');
            langEn.classList.remove('text-text-secondary');
            langAr.classList.remove('bg-primary', 'text-white');
            langAr.classList.add('text-text-secondary');
        });

        langAr.addEventListener('click', () => {
            htmlElement.setAttribute('lang', 'ar');
            htmlElement.setAttribute('dir', 'rtl');
            langAr.classList.add('bg-primary', 'text-white');
            langAr.classList.remove('text-text-secondary');
            langEn.classList.remove('bg-primary', 'text-white');
            langEn.classList.add('text-text-secondary');
        });

        // Component Library Functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeWorkbench();
            setupDragAndDrop();
            setupCanvasInteractions();
            setupAnalysisPanel();
            setupTemplates();
        });

        function initializeWorkbench() {
            // Filter functionality
            const filterBtns = document.querySelectorAll('.filter-btn');
            filterBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    filterBtns.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    filterComponents(btn.dataset.category);
                });
            });

            // Search functionality
            const searchInput = document.getElementById('componentSearch');
            searchInput.addEventListener('input', (e) => {
                searchComponents(e.target.value);
            });

            // Zoom controls
            document.getElementById('zoomIn').addEventListener('click', () => adjustZoom(0.1));
            document.getElementById('zoomOut').addEventListener('click', () => adjustZoom(-0.1));

            // Canvas controls
            document.getElementById('clearCanvas').addEventListener('click', clearCanvas);
            document.getElementById('saveCircuit').addEventListener('click', saveCircuit);
            document.getElementById('analyzeCircuit').addEventListener('click', analyzeCircuit);
        }

        function setupDragAndDrop() {
            const componentItems = document.querySelectorAll('.component-item');
            const canvas = document.getElementById('designCanvas');

            componentItems.forEach(item => {
                item.addEventListener('dragstart', (e) => {
                    e.dataTransfer.setData('text/plain', item.dataset.component);
                    item.classList.add('dragging');
                });

                item.addEventListener('dragend', () => {
                    item.classList.remove('dragging');
                });
            });

            canvas.addEventListener('dragover', (e) => {
                e.preventDefault();
                canvas.classList.add('drop-zone');
            });

            canvas.addEventListener('dragleave', () => {
                canvas.classList.remove('drop-zone');
            });

            canvas.addEventListener('drop', (e) => {
                e.preventDefault();
                canvas.classList.remove('drop-zone');
                
                const componentType = e.dataTransfer.getData('text/plain');
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                addComponentToCanvas(componentType, x, y);
            });
        }

        function addComponentToCanvas(componentType, x, y) {
            const container = document.getElementById('componentContainer');
            const component = document.createElement('div');
            component.className = 'absolute w-16 h-16 bg-surface border-2 border-slate-600 rounded-lg flex items-center justify-center cursor-move hover:border-primary-400 transition-colors';
            component.style.left = `${x - 32}px`;
            component.style.top = `${y - 32}px`;
            component.dataset.component = componentType;
            component.dataset.id = `comp_${++componentCounter}`;

            // Add component icon
            const icon = getComponentIcon(componentType);
            component.innerHTML = `<i class="${icon.class}" style="color: ${icon.color}"></i>`;

            // Add interaction handlers
            component.addEventListener('click', () => selectComponent(component));
            component.addEventListener('dblclick', () => openComponentModal(component));
            
            // Make draggable within canvas
            makeDraggable(component);

            container.appendChild(component);
            updateComponentCount();
        }

        function getComponentIcon(componentType) {
            const icons = {
                'op-amp-741': { class: 'fas fa-microchip', color: '#60a5fa' },
                'instrumentation-amp': { class: 'fas fa-microchip', color: '#10b981' },
                'low-pass-filter': { class: 'fas fa-wave-square', color: '#f59e0b' },
                'high-pass-filter': { class: 'fas fa-wave-square', color: '#f59e0b' },
                'notch-filter': { class: 'fas fa-wave-square', color: '#ef4444' },
                'ecg-electrode': { class: 'fas fa-heartbeat', color: '#10b981' },
                'eeg-electrode': { class: 'fas fa-brain', color: '#60a5fa' },
                'resistor': { class: 'fas fa-minus', color: '#f59e0b' },
                'capacitor': { class: 'fas fa-equals', color: '#f59e0b' }
            };
            return icons[componentType] || { class: 'fas fa-microchip', color: '#94a3b8' };
        }

        function makeDraggable(element) {
            let isDragging = false;
            let startX, startY, initialX, initialY;

            element.addEventListener('mousedown', (e) => {
                if (e.detail === 2) return; // Ignore double-click
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                initialX = parseInt(element.style.left);
                initialY = parseInt(element.style.top);
                element.style.zIndex = '1000';
            });

            document.addEventListener('mousemove', (e) => {
                if (!isDragging) return;
                e.preventDefault();
                
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                
                element.style.left = `${initialX + deltaX}px`;
                element.style.top = `${initialY + deltaY}px`;
            });

            document.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    element.style.zIndex = 'auto';
                }
            });
        }

        function selectComponent(component) {
            // Remove previous selection
            document.querySelectorAll('.component-selected').forEach(c => {
                c.classList.remove('component-selected');
            });
            
            // Add selection to current component
            component.classList.add('component-selected');
            selectedComponent = component;
        }

        function openComponentModal(component) {
            const modal = document.getElementById('componentModal');
            const title = document.getElementById('modalTitle');
            const content = document.getElementById('modalContent');
            
            title.textContent = `${component.dataset.component} Properties`;
            content.innerHTML = generateModalContent(component.dataset.component);
            
            modal.classList.remove('hidden');
        }

        function generateModalContent(componentType) {
            const properties = {
                'op-amp-741': `
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-text-secondary mb-1">Gain</label>
                            <input type="number" class="input-field w-full" value="100" min="1" max="10000">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-text-secondary mb-1">Bandwidth (Hz)</label>
                            <input type="number" class="input-field w-full" value="1000000" min="1" max="10000000">
                        </div>
                    </div>
                `,
                'resistor': `
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-text-secondary mb-1">Resistance (Ω)</label>
                            <input type="number" class="input-field w-full" value="1000" min="1" max="1000000">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-text-secondary mb-1">Tolerance (%)</label>
                            <select class="input-field w-full">
                                <option value="1">1%</option>
                                <option value="5" selected>5%</option>
                                <option value="10">10%</option>
                            </select>
                        </div>
                    </div>
                `
            };
            
            return properties[componentType] || '<p class="text-text-secondary">No configurable properties</p>';
        }

        function setupCanvasInteractions() {
            // Canvas click handler
            document.getElementById('designCanvas').addEventListener('click', (e) => {
                if (e.target.id === 'designCanvas') {
                    // Deselect all components
                    document.querySelectorAll('.component-selected').forEach(c => {
                        c.classList.remove('component-selected');
                    });
                    selectedComponent = null;
                }
            });

            // Modal handlers
            document.getElementById('closeModal').addEventListener('click', closeModal);
            document.getElementById('cancelModal').addEventListener('click', closeModal);
            document.getElementById('saveModal').addEventListener('click', saveModalChanges);
        }

        function setupAnalysisPanel() {
            const analyzeBtn = document.getElementById('analyzeCircuit');
            const panel = document.getElementById('analysisPanel');
            const closeBtn = document.getElementById('closeAnalysis');
            const tabs = document.querySelectorAll('.analysis-tab');

            analyzeBtn.addEventListener('click', () => {
                panel.classList.remove('translate-x-full');
                performAnalysis();
            });

            closeBtn.addEventListener('click', () => {
                panel.classList.add('translate-x-full');
            });

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    showAnalysisTab(tab.dataset.tab);
                });
            });
        }

        function performAnalysis() {
            // Simulate circuit analysis
            setTimeout(() => {
                document.getElementById('dcGain').textContent = '42.5 dB';
                document.getElementById('bandwidth').textContent = '1.2 MHz';
                document.getElementById('inputImpedance').textContent = '10 MΩ';
                document.getElementById('cmrr').textContent = '85 dB';
                document.getElementById('snr').textContent = '72 dB';
                document.getElementById('transferFunction').textContent = 'H(s) = 1000 / (1 + s/2π×1MHz)';
                document.getElementById('inputNoise').textContent = '2.5 nV/√Hz';
                document.getElementById('outputNoise').textContent = '105 μV/√Hz';
                document.getElementById('noiseFigure').textContent = '3.2 dB';
            }, 500);
        }

        function showAnalysisTab(tabName) {
            document.querySelectorAll('.analysis-content').forEach(content => {
                content.classList.add('hidden');
            });
            document.getElementById(`${tabName}Tab`).classList.remove('hidden');
        }

        function setupTemplates() {
            const templateBtns = document.querySelectorAll('.template-btn');
            templateBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    loadTemplate(btn.dataset.template);
                });
            });
        }

        function loadTemplate(templateName) {
            clearCanvas();
            
            const templates = {
                'ecg-amplifier': () => {
                    addComponentToCanvas('ecg-electrode', 100, 200);
                    addComponentToCanvas('instrumentation-amp', 250, 200);
                    addComponentToCanvas('low-pass-filter', 400, 200);
                    addComponentToCanvas('op-amp-741', 550, 200);
                },
                'eeg-filter': () => {
                    addComponentToCanvas('eeg-electrode', 100, 150);
                    addComponentToCanvas('high-pass-filter', 250, 150);
                    addComponentToCanvas('notch-filter', 400, 150);
                    addComponentToCanvas('low-pass-filter', 550, 150);
                }
            };
            
            if (templates[templateName]) {
                templates[templateName]();
            }
        }

        function filterComponents(category) {
            const categories = document.querySelectorAll('.component-category');
            categories.forEach(cat => {
                if (category === 'all' || cat.dataset.category === category) {
                    cat.style.display = 'block';
                } else {
                    cat.style.display = 'none';
                }
            });
        }

        function searchComponents(query) {
            const items = document.querySelectorAll('.component-item');
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(query.toLowerCase())) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function adjustZoom(delta) {
            currentZoom = Math.max(0.5, Math.min(2, currentZoom + delta));
            document.getElementById('zoomLevel').textContent = `${Math.round(currentZoom * 100)}%`;
            document.getElementById('componentContainer').style.transform = `scale(${currentZoom})`;
        }

        function clearCanvas() {
            document.getElementById('componentContainer').innerHTML = '';
            document.getElementById('wireContainer').innerHTML = '';
            componentCounter = 0;
            connectionCounter = 0;
            updateComponentCount();
        }

        function saveCircuit() {
            const components = Array.from(document.querySelectorAll('#componentContainer > div')).map(comp => ({
                type: comp.dataset.component,
                id: comp.dataset.id,
                x: parseInt(comp.style.left),
                y: parseInt(comp.style.top)
            }));
            
            const circuitData = {
                components,
                timestamp: new Date().toISOString(),
                zoom: currentZoom
            };
            
            localStorage.setItem('savedCircuit', JSON.stringify(circuitData));
            
            // Show success message
            showNotification('Circuit saved successfully!', 'success');
        }

        function analyzeCircuit() {
            const componentCount = document.querySelectorAll('#componentContainer > div').length;
            if (componentCount === 0) {
                showNotification('Add components to analyze the circuit', 'warning');
                return;
            }
            
            document.getElementById('analyzeCircuit').click();
        }

        function updateComponentCount() {
            document.getElementById('componentCount').textContent = componentCounter;
            document.getElementById('connectionCount').textContent = connectionCounter;
        }

        function closeModal() {
            document.getElementById('componentModal').classList.add('hidden');
        }

        function saveModalChanges() {
            // Save component property changes
            closeModal();
            showNotification('Component properties updated', 'success');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-dramatic transition-all duration-300 ${
                type === 'success' ? 'bg-secondary text-white' :
                type === 'warning' ? 'bg-accent text-white' :
                type === 'error' ? 'bg-error-500 text-white' :
                'bg-surface text-text-primary border border-slate-700'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Help button functionality
        document.getElementById('helpButton').addEventListener('click', () => {
            showNotification('Drag components from the library to the canvas. Double-click to configure properties.', 'info');
        });

        // Add CSS class for component selection
        const style = document.createElement('style');
        style.textContent = `
            .component-selected {
                border-color: #60a5fa !important;
                box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3) !important;
            }
        `;
        document.head.appendChild(style);
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
</body>
</html>