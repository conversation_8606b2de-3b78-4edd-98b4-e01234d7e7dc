<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biomedical Instrumentation Lab</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        /* Custom CSS for RTL and animations */
        html[dir="rtl"] body {
            text-align: right;
        }
        html[dir="rtl"] .nav-panel {
            border-left: none;
            border-right: 1px solid #ccc;
        }
        html[dir="rtl"] .module-item {
            padding-left: 0;
            padding-right: 1rem;
        }
        
        /* Pulse animation for icons */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        
        /* ECG line animation */
        .ecg-line {
            position: relative;
            overflow: hidden;
        }
        .ecg-line::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shine 2s infinite;
        }
        @keyframes shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        /* Smooth transitions */
        .smooth-transition {
            transition: all 0.3s ease;
        }
        
        /* Loading animation */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans flex flex-col min-h-screen">
    <!-- Header -->
    <header class="bg-blue-800 text-white shadow-lg">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fas fa-heartbeat text-2xl pulse text-red-500"></i>
                <h1 class="text-2xl font-bold" data-lang-key="app_title">Biomedical Instrumentation Lab</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <span class="mr-2" data-lang-key="language">Language:</span>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="languageToggle" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        <span class="ml-2 text-sm font-medium" id="languageLabel">English</span>
                    </label>
                </div>
                <div class="relative">
                    <div class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center cursor-pointer">
                        <i class="fas fa-user text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="flex flex-1 overflow-hidden">
        <!-- Navigation Panel -->
        <div class="nav-panel w-64 bg-white border-r border-gray-200 flex-shrink-0 overflow-y-auto smooth-transition">
            <div class="p-4">
                <div class="mb-4">
                    <h3 class="font-semibold text-gray-700 mb-2" data-lang-key="filter_by_level">Filter by Level</h3>
                    <div class="flex flex-wrap gap-2">
                        <button class="level-filter px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 smooth-transition" data-level="all" data-lang-key="all">All</button>
                        <button class="level-filter px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 smooth-transition" data-level="beginner" data-lang-key="beginner">Beginner</button>
                        <button class="level-filter px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 hover:bg-yellow-200 smooth-transition" data-level="intermediate" data-lang-key="intermediate">Intermediate</button>
                        <button class="level-filter px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 hover:bg-red-200 smooth-transition" data-level="advanced" data-lang-key="advanced">Advanced</button>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="relative">
                        <input type="text" id="moduleSearch" placeholder="Search modules..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" data-lang-key="search_placeholder">
                        <i class="fas fa-search absolute right-3 top-3 text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div id="moduleList" class="pb-4">
                <!-- Modules will be dynamically loaded here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 overflow-y-auto bg-gray-50 p-6">
            <div id="loadingIndicator" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white p-6 rounded-lg shadow-lg flex flex-col items-center">
                    <div class="loading-spinner w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full mb-4"></div>
                    <p data-lang-key="loading">Loading content...</p>
                </div>
            </div>
            
            <div id="contentArea">
                <!-- Welcome content -->
                <div class="text-center py-12">
                    <i class="fas fa-heartbeat text-6xl text-red-500 mb-4 pulse"></i>
                    <h2 class="text-3xl font-bold text-gray-800 mb-2" data-lang-key="welcome_title">Welcome to Biomedical Instrumentation Lab</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto" data-lang-key="welcome_message">Select a module from the left panel to begin your learning journey in biomedical measurement and instrumentation.</p>
                </div>
                
                <!-- Module content will be dynamically loaded here -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p data-lang-key="footer_author">Author: Dr. Mohammed Yagoub Esmail, SUST - BME</p>
                    <p data-lang-key="footer_copyright">© 2025. All Rights Reserved.</p>
                </div>
                <div class="flex flex-col md:flex-row md:space-x-6 space-y-2 md:space-y-0">
                    <a href="mailto:<EMAIL>" class="hover:text-blue-300 smooth-transition" data-lang-key="contact_email">Contact: <EMAIL></a>
                    <div class="flex space-x-4">
                        <a href="tel:+249912867327" class="hover:text-blue-300 smooth-transition">Phone: +249912867327</a>
                        <span>/</span>
                        <a href="tel:+966538076790" class="hover:text-blue-300 smooth-transition">+966538076790</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Language data
        const languageData = {
            en: {
                "app_title": "Biomedical Instrumentation Lab",
                "language": "Language:",
                "all": "All",
                "beginner": "Beginner",
                "intermediate": "Intermediate",
                "advanced": "Advanced",
                "search_placeholder": "Search modules...",
                "loading": "Loading content...",
                "welcome_title": "Welcome to Biomedical Instrumentation Lab",
                "welcome_message": "Select a module from the left panel to begin your learning journey in biomedical measurement and instrumentation.",
                "footer_author": "Author: Dr. Mohammed Yagoub Esmail, SUST - BME",
                "footer_copyright": "© 2025. All Rights Reserved.",
                "contact_email": "Contact: <EMAIL>",
                "ecg_bipolar_title": "ECG Fundamentals: Bipolar Leads",
                "ecg_12lead_title": "Monitoring Standard 12-Leads",
                "ecg_artifacts_title": "ECG Artifacts Identification",
                "ecg_pathology_title": "Pathological ECGs Analysis",
                "eeg_fundamentals_title": "EEG Fundamentals: Brain Waves",
                "eeg_analysis_title": "EEG Signal Analysis",
                "bp_measurement_title": "Blood Pressure Measurement",
                "bp_disorders_title": "Hypertension & Hypotension",
                "module_description": "Description",
                "module_theory": "Theory",
                "module_simulation": "Simulation",
                "module_analysis": "Analysis",
                "start_module": "Start Module",
                "drag_instruction": "Drag the electrodes to their correct positions.",
                "systolic_pressure": "Systolic Pressure",
                "diastolic_pressure": "Diastolic Pressure",
                "normal": "Normal",
                "hypertensive": "Hypertensive",
                "hypotensive": "Hypotensive",
                "select_patient": "Select Patient Profile",
                "measure_bp": "Measure Blood Pressure",
                "ecg_waveform": "ECG Waveform",
                "select_lead": "Select ECG Lead",
                "inject_artifact": "Inject Artifact",
                "clean_signal": "Clean Signal",
                "muscle_tremor": "Muscle Tremor",
                "baseline_wander": "Baseline Wander",
                "power_interference": "50Hz Interference",
                "identify_features": "Identify Key Features",
                "eeg_waveforms": "EEG Waveforms",
                "alpha_waves": "Alpha Waves",
                "beta_waves": "Beta Waves",
                "theta_waves": "Theta Waves",
                "delta_waves": "Delta Waves",
                "calculate_psd": "Calculate PSD",
                "frequency_spectrum": "Frequency Spectrum"
            },
            ar: {
                "app_title": "مختبر الأجهزة الطبية الحيوية",
                "language": "اللغة:",
                "all": "الكل",
                "beginner": "مبتدئ",
                "intermediate": "متوسط",
                "advanced": "متقدم",
                "search_placeholder": "ابحث عن الوحدات...",
                "loading": "جاري تحميل المحتوى...",
                "welcome_title": "مرحبًا بكم في مختبر الأجهزة الطبية الحيوية",
                "welcome_message": "حدد وحدة من اللوحة الجانبية لبدء رحلة التعلم في قياسات وأجهزة الطب الحيوي.",
                "footer_author": "المؤلف: د. محمد يعقوب إسماعيل، جامعة السودان للعلوم والتكنولوجيا - الهندسة الطبية الحيوية",
                "footer_copyright": "© 2025. جميع الحقوق محفوظة.",
                "contact_email": "للتواصل: <EMAIL>",
                "ecg_bipolar_title": "أساسيات تخطيط القلب: الأقطاب ثنائية القطب",
                "ecg_12lead_title": "مراقبة 12 قطبًا قياسيًا",
                "ecg_artifacts_title": "تحديد التشوهات في تخطيط القلب",
                "ecg_pathology_title": "تحليل تخطيط القلب المرضي",
                "eeg_fundamentals_title": "أساسيات تخطيط الدماغ: موجات الدماغ",
                "eeg_analysis_title": "تحليل إشارات تخطيط الدماغ",
                "bp_measurement_title": "قياس ضغط الدم",
                "bp_disorders_title": "ارتفاع وانخفاض ضغط الدم",
                "module_description": "الوصف",
                "module_theory": "النظرية",
                "module_simulation": "المحاكاة",
                "module_analysis": "التحليل",
                "start_module": "ابدأ الوحدة",
                "drag_instruction": "اسحب الأقطاب الكهربائية إلى مواضعها الصحيحة.",
                "systolic_pressure": "الضغط الانقباضي",
                "diastolic_pressure": "الضغط الانبساطي",
                "normal": "طبيعي",
                "hypertensive": "مصاب بارتفاع الضغط",
                "hypotensive": "مصاب بانخفاض الضغط",
                "select_patient": "اختر حالة المريض",
                "measure_bp": "قياس ضغط الدم",
                "ecg_waveform": "موجة تخطيط القلب",
                "select_lead": "اختر قطب التخطيط",
                "inject_artifact": "إضافة تشوه",
                "clean_signal": "إشارة نظيفة",
                "muscle_tremor": "رعشة العضلات",
                "baseline_wander": "تذبذب الخط الأساسي",
                "power_interference": "تشوش التيار الكهربائي",
                "identify_features": "حدد الملامح الرئيسية",
                "eeg_waveforms": "موجات تخطيط الدماغ",
                "alpha_waves": "موجات ألفا",
                "beta_waves": "موجات بيتا",
                "theta_waves": "موجات ثيتا",
                "delta_waves": "موجات دلتا",
                "calculate_psd": "حساب كثافة القدرة الطيفية",
                "frequency_spectrum": "الطيف الترددي"
            }
        };

        // Module data
        const modules = [
            {
                id: 'ecg-bipolar',
                titleKey: 'ecg_bipolar_title',
                difficulty: 'beginner',
                icon: 'heartbeat',
                description: 'Learn the fundamentals of ECG bipolar leads and Einthoven\'s Triangle.',
                content: {
                    theory: `
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold mb-4" data-lang-key="module_theory">Theory</h3>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <p class="mb-4">Electrocardiography (ECG) is a method of recording the electrical activity of the heart over time. The standard ECG has 12 leads:</p>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <h4 class="font-medium mb-2">Bipolar Limb Leads (I, II, III)</h4>
                                        <ul class="list-disc pl-5 space-y-1">
                                            <li>Lead I: Right arm (-) to Left arm (+)</li>
                                            <li>Lead II: Right arm (-) to Left leg (+)</li>
                                            <li>Lead III: Left arm (-) to Left leg (+)</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5a/Einthoven_triangle.svg/1200px-Einthoven_triangle.svg.png" alt="Einthoven's Triangle" class="w-full rounded">
                                    </div>
                                </div>
                                <div class="bg-blue-50 p-4 rounded border border-blue-200">
                                    <h4 class="font-medium text-blue-800 mb-2">Key Concept: Einthoven's Triangle</h4>
                                    <p>The three bipolar limb leads form an equilateral triangle (Einthoven's triangle) with the heart at the center. The electrical axis of the heart can be determined from these leads.</p>
                                </div>
                            </div>
                        </div>
                    `,
                    simulation: `
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold mb-4" data-lang-key="module_simulation">Simulation</h3>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <p class="mb-4" data-lang-key="drag_instruction">Drag the electrodes to their correct positions.</p>
                                
                                <div class="relative bg-gray-100 rounded-lg p-4 mb-4" style="height: 300px;">
                                    <!-- Human torso image with draggable electrodes -->
                                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/4/4f/Human_body_features_arabic.svg/800px-Human_body_features_arabic.svg.png" alt="Human torso" class="absolute inset-0 w-full h-full object-contain">
                                    
                                    <!-- Draggable electrodes -->
                                    <div class="absolute w-8 h-8 bg-red-500 rounded-full cursor-move flex items-center justify-center text-white font-bold" style="top: 30%; left: 20%;" data-electrode="RA">RA</div>
                                    <div class="absolute w-8 h-8 bg-red-500 rounded-full cursor-move flex items-center justify-center text-white font-bold" style="top: 30%; left: 80%;" data-electrode="LA">LA</div>
                                    <div class="absolute w-8 h-8 bg-red-500 rounded-full cursor-move flex items-center justify-center text-white font-bold" style="top: 80%; left: 50%;" data-electrode="LL">LL</div>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="bg-gray-50 p-3 rounded border">
                                        <h4 class="font-medium mb-2">Lead I</h4>
                                        <div class="ecg-line bg-white p-2 rounded" style="height: 100px;">
                                            <canvas id="leadI" height="100"></canvas>
                                        </div>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded border">
                                        <h4 class="font-medium mb-2">Lead II</h4>
                                        <div class="ecg-line bg-white p-2 rounded" style="height: 100px;">
                                            <canvas id="leadII" height="100"></canvas>
                                        </div>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded border">
                                        <h4 class="font-medium mb-2">Lead III</h4>
                                        <div class="ecg-line bg-white p-2 rounded" style="height: 100px;">
                                            <canvas id="leadIII" height="100"></canvas>
                                        </div>
                                    </div>
                                </div>
                                
                                <button id="checkPlacement" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 smooth-transition" data-lang-key="start_module">Check Placement</button>
                            </div>
                        </div>
                    `
                }
            },
            {
                id: 'ecg-12lead',
                titleKey: 'ecg_12lead_title',
                difficulty: 'beginner',
                icon: 'heartbeat',
                description: 'Explore the standard 12-lead ECG system and interpret waveforms.',
                content: {
                    theory: `
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold mb-4" data-lang-key="module_theory">Theory</h3>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <p class="mb-4">The standard 12-lead ECG provides a comprehensive view of the heart's electrical activity from 12 different angles:</p>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <h4 class="font-medium mb-2">Limb Leads</h4>
                                        <ul class="list-disc pl-5 space-y-1">
                                            <li>3 Bipolar leads (I, II, III)</li>
                                            <li>3 Augmented unipolar leads (aVR, aVL, aVF)</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h4 class="font-medium mb-2">Precordial Leads</h4>
                                        <ul class="list-disc pl-5 space-y-1">
                                            <li>6 Unipolar leads (V1-V6)</li>
                                            <li>Placed across the chest in specific positions</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="bg-blue-50 p-4 rounded border border-blue-200">
                                    <h4 class="font-medium text-blue-800 mb-2">Clinical Significance</h4>
                                    <p>The 12-lead ECG allows clinicians to assess the heart's electrical activity from multiple perspectives, helping to localize ischemic changes, arrhythmias, and other cardiac abnormalities.</p>
                                </div>
                            </div>
                        </div>
                    `,
                    simulation: `
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold mb-4" data-lang-key="module_simulation">Simulation</h3>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <div class="mb-4">
                                    <label class="block text-sm font-medium mb-1" data-lang-key="select_lead">Select ECG Lead</label>
                                    <select id="ecgLeadSelect" class="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500">
                                        <option value="I" data-lang-key="lead_I">Lead I</option>
                                        <option value="II" data-lang-key="lead_II">Lead II</option>
                                        <option value="III" data-lang-key="lead_III">Lead III</option>
                                        <option value="aVR" data-lang-key="lead_aVR">Lead aVR</option>
                                        <option value="aVL" data-lang-key="lead_aVL">Lead aVL</option>
                                        <option value="aVF" data-lang-key="lead_aVF">Lead aVF</option>
                                        <option value="V1" data-lang-key="lead_V1">Lead V1</option>
                                        <option value="V2" data-lang-key="lead_V2">Lead V2</option>
                                        <option value="V3" data-lang-key="lead_V3">Lead V3</option>
                                        <option value="V4" data-lang-key="lead_V4">Lead V4</option>
                                        <option value="V5" data-lang-key="lead_V5">Lead V5</option>
                                        <option value="V6" data-lang-key="lead_V6">Lead V6</option>
                                    </select>
                                </div>
                                
                                <div class="bg-gray-50 p-4 rounded border mb-4">
                                    <h4 class="font-medium mb-2" data-lang-key="ecg_waveform">ECG Waveform</h4>
                                    <div class="ecg-line bg-white p-2 rounded" style="height: 200px;">
                                        <canvas id="ecgWaveform" height="200"></canvas>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h4 class="font-medium mb-2">Waveform Components</h4>
                                        <ul class="list-disc pl-5 space-y-1">
                                            <li>P wave: Atrial depolarization</li>
                                            <li>QRS complex: Ventricular depolarization</li>
                                            <li>T wave: Ventricular repolarization</li>
                                            <li>PR interval: Atrial to ventricular conduction</li>
                                            <li>QT interval: Ventricular depolarization and repolarization</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h4 class="font-medium mb-2">Normal Values</h4>
                                        <ul class="list-disc pl-5 space-y-1">
                                            <li>Heart rate: 60-100 bpm</li>
                                            <li>PR interval: 120-200 ms</li>
                                            <li>QRS duration: <120 ms</li>
                                            <li>QT interval: <440 ms (varies with heart rate)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `
                }
            },
            {
                id: 'ecg-artifacts',
                titleKey: 'ecg_artifacts_title',
                difficulty: 'intermediate',
                icon: 'heartbeat',
                description: 'Identify and understand common ECG artifacts and their sources.',
                content: {
                    theory: `
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold mb-4" data-lang-key="module_theory">Theory</h3>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <p class="mb-4">ECG artifacts are waveforms that mimic cardiac electrical activity but are actually caused by external sources. Recognizing artifacts is crucial to avoid misdiagnosis.</p>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <h4 class="font-medium mb-2">Common ECG Artifacts</h4>
                                        <ul class="list-disc pl-5 space-y-1">
                                            <li>Baseline wander (respiratory movement)</li>
                                            <li>Muscle tremor (EMG interference)</li>
                                            <li>60Hz power line interference</li>
                                            <li>Electrode motion artifacts</li>
                                            <li>Poor electrode contact</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <img src="https://ecgwaves.com/wp-content/uploads/2019/11/ECG-artifacts-examples-ECGWaves.png" alt="ECG Artifacts" class="w-full rounded">
                                    </div>
                                </div>
                                
                                <div class="bg-blue-50 p-4 rounded border border-blue-200">
                                    <h4 class="font-medium text-blue-800 mb-2">Differentiating Artifacts from Arrhythmias</h4>
                                    <p>Artifacts often appear irregular, lack the typical P-QRS-T sequence, and may correlate with patient movement or equipment issues. True arrhythmias will typically show consistent abnormal patterns.</p>
                                </div>
                            </div>
                        </div>
                    `,
                    simulation: `
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold mb-4" data-lang-key="module_simulation">Simulation</h3>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <div class="mb-4">
                                    <label class="block text-sm font-medium mb-1" data-lang-key="select_lead">Select ECG Lead</label>
                                    <select id="artifactLeadSelect" class="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500">
                                        <option value="II">Lead II</option>
                                    </select>
                                </div>
                                
                                <div class="bg-gray-50 p-4 rounded border mb-4">
                                    <h4 class="font-medium mb-2" data-lang-key="ecg_waveform">ECG Waveform</h4>
                                    <div class="ecg-line bg-white p-2 rounded" style="height: 200px;">
                                        <canvas id="artifactECG" height="200"></canvas>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <label class="block text-sm font-medium mb-1" data-lang-key="inject_artifact">Inject Artifact</label>
                                    <div class="flex flex-wrap gap-2">
                                        <button class="artifact-btn px-3 py-1 bg-green-100 text-green-800 rounded hover:bg-green-200 smooth-transition" data-artifact="clean" data-lang-key="clean_signal">Clean Signal</button>
                                        <button class="artifact-btn px-3 py-1 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 smooth-transition" data-artifact="tremor" data-lang-key="muscle_tremor">Muscle Tremor</button>
                                        <button class="artifact-btn px-3 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200 smooth-transition" data-artifact="wander" data-lang-key="baseline_wander">Baseline Wander</button>
                                        <button class="artifact-btn px-3 py-1 bg-purple-100 text-purple-800 rounded hover:bg-purple-200 smooth-transition" data-artifact="power" data-lang-key="power_interference">50Hz Interference</button>
                                    </div>
                                </div>
                                
                                <div class="bg-blue-50 p-4 rounded border">
                                    <h4 class="font-medium text-blue-800 mb-2">Artifact Identification Challenge</h4>
                                    <p class="mb-2">The ECG above now shows an artifact. Can you identify which type it is?</p>
                                    <div class="flex flex-wrap gap-2">
                                        <button class="guess-btn px-3 py-1 bg-gray-100 text-gray-800 rounded hover:bg-gray-200 smooth-transition" data-guess="tremor" data-lang-key="muscle_tremor">Muscle Tremor</button>
                                        <button class="guess-btn px-3 py-1 bg-gray-100 text-gray-800 rounded hover:bg-gray-200 smooth-transition" data-guess="wander" data-lang-key="baseline_wander">Baseline Wander</button>
                                        <button class="guess-btn px-3 py-1 bg-gray-100 text-gray-800 rounded hover:bg-gray-200 smooth-transition" data-guess="power" data-lang-key="power_interference">50Hz Interference</button>
                                    </div>
                                    <p id="artifactFeedback" class="mt-2 hidden"></p>
                                </div>
                            </div>
                        </div>
                    `
                }
            },
            {
                id: 'ecg-pathology',
                titleKey: 'ecg_pathology_title',
                difficulty: 'advanced',
                icon: 'heartbeat',
                description: 'Analyze pathological ECG patterns and their clinical significance.',
                content: {
                    theory: `
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold mb-4" data-lang-key="module_theory">Theory</h3>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <p class="mb-4">Pathological ECGs reveal abnormalities in the heart's electrical activity that correlate with specific cardiac conditions. Recognizing these patterns is essential for diagnosis and treatment.</p>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <h4 class="font-medium mb-2">Common Pathological Patterns</h4>
                                        <ul class="list-disc pl-5 space-y-1">
                                            <li>ST elevation (STEMI)</li>
                                            <li>ST depression (ischemia)</li>
                                            <li>Pathological Q waves (infarction)</li>
                                            <li>QT prolongation</li>
                                            <li>Bundle branch blocks</li>
                                            <li>Atrial fibrillation</li>
                                            <li>Ventricular tachycardia</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <img src="https://ecgwaves.com/wp-content/uploads/2019/11/ECG-pathologies-examples-ECGWaves.png" alt="Pathological ECGs" class="w-full rounded">
                                    </div>
                                </div>
                                
                                <div class="bg-blue-50 p-4 rounded border border-blue-200">
                                    <h4 class="font-medium text-blue-800 mb-2">Clinical Correlation</h4>
                                    <p>ECG findings must always be interpreted in the context of the patient's symptoms, medical history, and other diagnostic tests. Some patterns are diagnostic (e.g., STEMI), while others require correlation with clinical findings.</p>
                                </div>
                            </div>
                        </div>
                    `,
                    simulation: `
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold mb-4" data-lang-key="module_simulation">Simulation</h3>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <div class="mb-4">
                                    <label class="block text-sm font-medium mb-1">Select Pathology</label>
                                    <select id="pathologySelect" class="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500">
                                        <option value="normal">Normal ECG</option>
                                        <option value="stemi">ST Elevation MI (STEMI)</option>
                                        <option value="afib">Atrial Fibrillation</option>
                                        <option value="vtach">Ventricular Tachycardia</option>
                                        <option value="bbb">Bundle Branch Block</option>
                                    </select>
                                </div>
                                
                                <div class="bg-gray-50 p-4 rounded border mb-4">
                                    <h4 class="font-medium mb-2">ECG Waveform</h4>
                                    <div class="ecg-line bg-white p-2 rounded" style="height: 200px;">
                                        <canvas id="pathologyECG" height="200"></canvas>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <label class="block text-sm font-medium mb-1" data-lang-key="identify_features">Identify Key Features</label>
                                    <div class="bg-white p-3 rounded border">
                                        <p class="mb-2">Click on the ECG waveform to mark the following features:</p>
                                        <div id="featureList" class="space-y-2">
                                            <!-- Features will be dynamically loaded based on selected pathology -->
                                        </div>
                                        <button id="checkFeatures" class="mt-2 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 smooth-transition" data-lang-key="start_module">Check Features</button>
                                        <p id="featureFeedback" class="mt-2 hidden"></p>
                                    </div>
                                </div>
                                
                                <div class="bg-blue-50 p-4 rounded border">
                                    <h4 class="font-medium text-blue-800 mb-2">Clinical Significance</h4>
                                    <div id="pathologyExplanation">
                                        <!-- Explanation will be dynamically loaded -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    `
                }
            },
            {
                id: 'eeg-fundamentals',
                titleKey: 'eeg_fundamentals_title',
                difficulty: 'beginner',
                icon: 'brain',
                description: 'Learn about EEG fundamentals and brain wave patterns.',
                content: {
                    theory: `
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold mb-4" data-lang-key="module_theory">Theory</h3>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <p class="mb-4">Electroencephalography (EEG) records electrical activity of the brain using electrodes placed on the scalp. Different brain wave patterns correlate with various states of consciousness and brain function.</p>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <h4 class="font-medium mb-2">10-20 Electrode Placement System</h4>
                                        <ul class="list-disc pl-5 space-y-1">
                                            <li>Standardized method for electrode placement</li>
                                            <li>Letters represent brain regions (F=Frontal, T=Temporal, etc.)</li>
                                            <li>Numbers indicate laterality (odd=left, even=right)</li>
                                            <li>Z indicates midline electrodes</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d5/21_electrodes_of_International_10-20_system_for_EEG.svg/1200px-21_electrodes_of_International_10-20_system_for_EEG.svg.png" alt="10-20 System" class="w-full rounded">
                                    </div>
                                </div>
                                
                                <div class="bg-blue-50 p-4 rounded border border-blue-200">
                                    <h4 class="font-medium text-blue-800 mb-2">Brain Wave Frequencies</h4>
                                    <p>EEG signals are classified by frequency bands that correlate with different mental states and cognitive functions.</p>
                                </div>
                            </div>
                        </div>
                    `,
                    simulation: `
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold mb-4" data-lang-key="module_simulation">Simulation</h3>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <div class="mb-4">
                                    <label class="block text-sm font-medium mb-1">Select Brain Wave Type</label>
                                    <select id="eegWaveSelect" class="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500">
                                        <option value="alpha" data-lang-key="alpha_waves">Alpha Waves</option>
                                        <option value="beta" data-lang-key="beta_waves">Beta Waves</option>
                                        <option value="theta" data-lang-key="theta_waves">Theta Waves</option>
                                        <option value="delta" data-lang-key="delta_waves">Delta Waves</option>
                                    </select>
                                </div>
                                
                                <div class="bg-gray-50 p-4 rounded border mb-4">
                                    <h4 class="font-medium mb-2" data-lang-key="eeg_waveforms">EEG Waveforms</h4>
                                    <div class="bg-white p-2 rounded" style="height: 200px;">
                                        <canvas id="eegWaveform" height="200"></canvas>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h4 class="font-medium mb-2">Characteristics</h4>
                                        <ul class="list-disc pl-5 space-y-1">
                                            <li id="eegFreq">Frequency: 8-13 Hz</li>
                                            <li id="eegAmp">Amplitude: 20-200 μV</li>
                                            <li id="eegLoc">Location: Occipital regions</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h4 class="font-medium mb-2">Mental State</h4>
                                        <p id="eegState">Relaxed wakefulness, eyes closed</p>
                                    </div>
                                </div>
                                
                                <div class="bg-blue-50 p-4 rounded border mt-4">
                                    <h4 class="font-medium text-blue-800 mb-2">Clinical Applications</h4>
                                    <p>EEG is used to diagnose epilepsy, sleep disorders, encephalopathies, and to monitor brain function during surgery or coma.</p>
                                </div>
                            </div>
                        </div>
                    `
                }
            },
            {
                id: 'eeg-analysis',
                titleKey: 'eeg_analysis_title',
                difficulty: 'advanced',
                icon: 'brain',
                description: 'Perform spectral analysis of EEG signals and interpret results.',
                content: {
                    theory: `
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold mb-4" data-lang-key="module_theory">Theory</h3>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <p class="mb-4">Advanced EEG analysis involves mathematical processing of the raw signal to extract clinically relevant information. Spectral analysis decomposes the EEG into its constituent frequencies.</p>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <h4 class="font-medium mb-2">Power Spectral Density (PSD)</h4>
                                        <ul class="list-disc pl-5 space-y-1">
                                            <li>Quantifies power distribution across frequencies</li>
                                            <li>Calculated using Fourier Transform</li>
                                            <li>Reveals dominant frequencies in the EEG</li>
                                            <li>Useful for detecting abnormal rhythms</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <img src="https://www.researchgate.net/publication/334506505/figure/fig1/AS:781652843778048@1563993821863/Power-spectral-density-estimation-of-an-EEG-signal-using-the-Welch-method-The-power.png" alt="PS
</body>
</html>