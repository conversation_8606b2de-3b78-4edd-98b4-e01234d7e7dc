module.exports = {
  content: ["./pages/*.{html,js}", "./index.html", "./js/*.js"],
  theme: {
    extend: {
      colors: {
        // Primary Colors - Medical Authority
        primary: {
          DEFAULT: "#1e3a8a", // blue-800
          50: "#eff6ff", // blue-50
          100: "#dbeafe", // blue-100
          200: "#bfdbfe", // blue-200
          300: "#93c5fd", // blue-300
          400: "#60a5fa", // blue-400
          500: "#3b82f6", // blue-500
          600: "#2563eb", // blue-600
          700: "#1d4ed8", // blue-700
          800: "#1e3a8a", // blue-800
          900: "#1e40af", // blue-900
        },
        // Secondary Colors - Success & Learning Feedback
        secondary: {
          DEFAULT: "#10b981", // emerald-500
          50: "#ecfdf5", // emerald-50
          100: "#d1fae5", // emerald-100
          200: "#a7f3d0", // emerald-200
          300: "#6ee7b7", // emerald-300
          400: "#34d399", // emerald-400
          500: "#10b981", // emerald-500
          600: "#059669", // emerald-600
          700: "#047857", // emerald-700
          800: "#065f46", // emerald-800
          900: "#064e3b", // emerald-900
        },
        // Accent Colors - Interactive Elements & Achievements
        accent: {
          DEFAULT: "#f59e0b", // amber-500
          50: "#fffbeb", // amber-50
          100: "#fef3c7", // amber-100
          200: "#fde68a", // amber-200
          300: "#fcd34d", // amber-300
          400: "#fbbf24", // amber-400
          500: "#f59e0b", // amber-500
          600: "#d97706", // amber-600
          700: "#b45309", // amber-700
          800: "#92400e", // amber-800
          900: "#78350f", // amber-900
        },
        // Background Colors
        background: "#0f172a", // slate-900
        surface: {
          DEFAULT: "#1e293b", // slate-800
          light: "#334155", // slate-700
        },
        // Text Colors
        text: {
          primary: "#f8fafc", // slate-50
          secondary: "#94a3b8", // slate-400
          muted: "#64748b", // slate-500
        },
        // Status Colors
        success: {
          DEFAULT: "#22c55e", // green-500
          50: "#f0fdf4", // green-50
          100: "#dcfce7", // green-100
          500: "#22c55e", // green-500
          600: "#16a34a", // green-600
        },
        warning: {
          DEFAULT: "#eab308", // yellow-500
          50: "#fefce8", // yellow-50
          100: "#fef9c3", // yellow-100
          500: "#eab308", // yellow-500
          600: "#ca8a04", // yellow-600
        },
        error: {
          DEFAULT: "#ef4444", // red-500
          50: "#fef2f2", // red-50
          100: "#fee2e2", // red-100
          500: "#ef4444", // red-500
          600: "#dc2626", // red-600
        },
      },
      fontFamily: {
        // Headlines - Inter for modern clarity
        inter: ['Inter', 'sans-serif'],
        // Body - Source Sans Pro for technical readability
        'source-sans': ['Source Sans Pro', 'sans-serif'],
        // Technical elements - JetBrains Mono
        mono: ['JetBrains Mono', 'monospace'],
        // Default sans fallback
        sans: ['Inter', 'Source Sans Pro', 'sans-serif'],
      },
      fontSize: {
        // Custom font sizes for better hierarchy
        'hero': ['3.5rem', { lineHeight: '1.1', fontWeight: '700' }],
        'display': ['2.5rem', { lineHeight: '1.2', fontWeight: '600' }],
        'heading': ['2rem', { lineHeight: '1.3', fontWeight: '600' }],
        'subheading': ['1.5rem', { lineHeight: '1.4', fontWeight: '500' }],
        'body-lg': ['1.125rem', { lineHeight: '1.6', fontWeight: '400' }],
        'body': ['1rem', { lineHeight: '1.6', fontWeight: '400' }],
        'body-sm': ['0.875rem', { lineHeight: '1.5', fontWeight: '400' }],
        'caption': ['0.75rem', { lineHeight: '1.4', fontWeight: '400' }],
      },
      boxShadow: {
        'subtle': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'moderate': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'dramatic': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        'glow-primary': '0 0 20px rgba(30, 58, 138, 0.3)',
        'glow-secondary': '0 0 20px rgba(16, 185, 129, 0.3)',
        'glow-accent': '0 0 20px rgba(245, 158, 11, 0.3)',
      },
      animation: {
        'fade-in': 'fadeIn 300ms ease-out',
        'slide-up': 'slideUp 400ms ease-out',
        'celebration': 'celebration 600ms ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      transitionDuration: {
        'fast': '300ms',
        'moderate': '400ms',
        'slow': '600ms',
      },
      transitionTimingFunction: {
        'physics': 'cubic-bezier(0.34, 1.56, 0.64, 1)',
      },
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      backdropBlur: {
        'xs': '2px',
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
    },
  },
  plugins: [
    // Add any additional plugins here if needed
  ],
}