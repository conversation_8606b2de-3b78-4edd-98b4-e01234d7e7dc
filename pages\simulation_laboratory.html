<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Simulation Laboratory - BioMed Lab Pro</title>
    <link rel="stylesheet" href="../css/main.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fbiomedlab3601back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.6"></script>
</head>
<body class="bg-background text-text-primary font-inter min-h-screen">
    <!-- Header Command Center -->
    <header class="bg-surface border-b border-slate-700 sticky top-0 z-50 backdrop-blur-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo and Brand -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-heartbeat text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gradient-primary">BioMed Lab Pro</h1>
                            <p class="text-xs text-text-secondary">Educational Technology Platform</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="dashboard_hub.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                    </a>
                    <a href="module_explorer.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-compass mr-2"></i>Explore
                    </a>
                    <a href="simulation_laboratory.html" class="text-primary-400 font-semibold border-b-2 border-primary-400 pb-1">
                        <i class="fas fa-flask mr-2"></i>Laboratory
                    </a>
                    <a href="circuit_design_workbench.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-microchip mr-2"></i>Workbench
                    </a>
                </nav>

                <!-- Bilingual Toggle and User Actions -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center bg-surface-light rounded-lg p-1">
                        <button id="langEn" class="px-3 py-1 rounded-md text-sm font-medium bg-primary text-white transition-all duration-300">
                            EN
                        </button>
                        <button id="langAr" class="px-3 py-1 rounded-md text-sm font-medium text-text-secondary hover:text-white transition-all duration-300">
                            عربي
                        </button>
                    </div>
                    <div class="w-8 h-8 bg-gradient-to-br from-accent-400 to-accent-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <button class="md:hidden text-text-secondary hover:text-white">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Laboratory Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Laboratory Header -->
        <section class="mb-8">
            <div class="bg-gradient-to-r from-primary-900/50 to-secondary-900/50 rounded-2xl p-8 border border-slate-700">
                <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between">
                    <div class="flex-1">
                        <h2 class="text-3xl font-bold text-gradient-primary mb-2">Simulation Laboratory</h2>
                        <p class="text-text-secondary text-lg mb-4">Interactive biomedical signal analysis and monitoring</p>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-secondary rounded-full animate-pulse"></div>
                                <span class="text-sm text-text-secondary">Real-time simulation active</span>
                            </div>
                            <div class="text-sm text-text-secondary">Last updated: July 18, 2025</div>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 lg:ml-8">
                        <div class="flex items-center space-x-4">
                            <button id="resetSimulation" class="btn-accent text-sm px-4 py-2">
                                <i class="fas fa-redo mr-2"></i>Reset All
                            </button>
                            <button id="saveProgress" class="btn-secondary text-sm px-4 py-2">
                                <i class="fas fa-save mr-2"></i>Save Progress
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Laboratory Interface -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Component Palette -->
            <div class="lg:col-span-1">
                <div class="card sticky top-24">
                    <h3 class="text-lg font-semibold text-white mb-4">Medical Components</h3>
                    
                    <!-- ECG Components -->
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-text-secondary mb-3 uppercase tracking-wide">ECG Electrodes</h4>
                        <div class="space-y-2">
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="ecg-lead" data-name="Lead I">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                                        <i class="fas fa-circle text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">Lead I</p>
                                        <p class="text-xs text-text-secondary">RA to LA</p>
                                    </div>
                                </div>
                            </div>
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="ecg-lead" data-name="Lead II">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                        <i class="fas fa-circle text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">Lead II</p>
                                        <p class="text-xs text-text-secondary">RA to LL</p>
                                    </div>
                                </div>
                            </div>
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="ecg-lead" data-name="Lead III">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center">
                                        <i class="fas fa-circle text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">Lead III</p>
                                        <p class="text-xs text-text-secondary">LA to LL</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- EEG Components -->
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-text-secondary mb-3 uppercase tracking-wide">EEG Electrodes</h4>
                        <div class="space-y-2">
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="eeg-electrode" data-name="Frontal">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary-400 rounded-full flex items-center justify-center">
                                        <i class="fas fa-brain text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">Frontal</p>
                                        <p class="text-xs text-text-secondary">F3, F4, Fz</p>
                                    </div>
                                </div>
                            </div>
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="eeg-electrode" data-name="Parietal">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-secondary-400 rounded-full flex items-center justify-center">
                                        <i class="fas fa-brain text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">Parietal</p>
                                        <p class="text-xs text-text-secondary">P3, P4, Pz</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- BP Components -->
                    <div>
                        <h4 class="text-sm font-medium text-text-secondary mb-3 uppercase tracking-wide">BP Monitoring</h4>
                        <div class="space-y-2">
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="bp-cuff" data-name="BP Cuff">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-error-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-heartbeat text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">BP Cuff</p>
                                        <p class="text-xs text-text-secondary">Arm placement</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Simulation Canvas -->
            <div class="lg:col-span-3">
                <div class="space-y-6">
                    <!-- Simulation Controls -->
                    <div class="card">
                        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-white mb-4 sm:mb-0">Simulation Controls</h3>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-2">
                                    <label class="text-sm text-text-secondary">Simulation Type:</label>
                                    <select id="simulationType" class="input-field text-sm py-2">
                                        <option value="ecg">ECG Analysis</option>
                                        <option value="eeg">EEG Monitoring</option>
                                        <option value="bp">Blood Pressure</option>
                                    </select>
                                </div>
                                <button id="startSimulation" class="btn-primary text-sm px-4 py-2">
                                    <i class="fas fa-play mr-2"></i>Start
                                </button>
                                <button id="pauseSimulation" class="btn-accent text-sm px-4 py-2">
                                    <i class="fas fa-pause mr-2"></i>Pause
                                </button>
                            </div>
                        </div>

                        <!-- Parameter Controls -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm text-text-secondary mb-2">Heart Rate (BPM)</label>
                                <input type="range" id="heartRate" min="40" max="180" value="72" class="w-full h-2 bg-surface-light rounded-lg appearance-none cursor-pointer" />
                                <div class="flex justify-between text-xs text-text-secondary mt-1">
                                    <span>40</span>
                                    <span id="heartRateValue" class="text-primary-400 font-medium">72</span>
                                    <span>180</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm text-text-secondary mb-2">Amplitude</label>
                                <input type="range" id="amplitude" min="0.5" max="2.0" step="0.1" value="1.0" class="w-full h-2 bg-surface-light rounded-lg appearance-none cursor-pointer" />
                                <div class="flex justify-between text-xs text-text-secondary mt-1">
                                    <span>0.5</span>
                                    <span id="amplitudeValue" class="text-secondary-400 font-medium">1.0</span>
                                    <span>2.0</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm text-text-secondary mb-2">Noise Level</label>
                                <input type="range" id="noiseLevel" min="0" max="0.3" step="0.05" value="0.1" class="w-full h-2 bg-surface-light rounded-lg appearance-none cursor-pointer" />
                                <div class="flex justify-between text-xs text-text-secondary mt-1">
                                    <span>0</span>
                                    <span id="noiseLevelValue" class="text-accent-400 font-medium">0.1</span>
                                    <span>0.3</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Waveform Display -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-white">Real-time Waveform</h3>
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-secondary rounded-full animate-pulse"></div>
                                <span class="text-sm text-text-secondary">Live Signal</span>
                            </div>
                        </div>
                        <div class="relative">
                            <canvas id="waveformChart" class="w-full h-64 bg-surface-light/30 rounded-lg"></canvas>
                            <div class="absolute top-4 left-4 bg-surface/80 rounded-lg p-2">
                                <div class="text-xs text-text-secondary">
                                    <div>Frequency: <span id="frequencyDisplay" class="text-primary-400">1.2 Hz</span></div>
                                    <div>Amplitude: <span id="amplitudeDisplay" class="text-secondary-400">1.0 mV</span></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Electrode Placement Game -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-white">Electrode Placement Training</h3>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-text-secondary">Accuracy:</span>
                                <span id="placementAccuracy" class="text-accent-400 font-medium">0%</span>
                            </div>
                        </div>
                        
                        <!-- Human Body Diagram -->
                        <div class="relative bg-surface-light/30 rounded-lg p-8 min-h-96">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <img src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Human Body Diagram" class="w-48 h-80 object-contain opacity-60" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                            </div>
                            
                            <!-- Placement Zones -->
                            <div id="placementZones" class="relative z-10">
                                <!-- ECG Placement Zones -->
                                <div class="placement-zone absolute" style="top: 25%; left: 20%;" data-zone="ra" data-correct="false">
                                    <div class="w-8 h-8 border-2 border-dashed border-primary-400 rounded-full flex items-center justify-center hover:bg-primary-400/20 transition-colors duration-300">
                                        <span class="text-xs text-primary-400">RA</span>
                                    </div>
                                </div>
                                <div class="placement-zone absolute" style="top: 25%; right: 20%;" data-zone="la" data-correct="false">
                                    <div class="w-8 h-8 border-2 border-dashed border-primary-400 rounded-full flex items-center justify-center hover:bg-primary-400/20 transition-colors duration-300">
                                        <span class="text-xs text-primary-400">LA</span>
                                    </div>
                                </div>
                                <div class="placement-zone absolute" style="bottom: 25%; left: 45%;" data-zone="ll" data-correct="false">
                                    <div class="w-8 h-8 border-2 border-dashed border-primary-400 rounded-full flex items-center justify-center hover:bg-primary-400/20 transition-colors duration-300">
                                        <span class="text-xs text-primary-400">LL</span>
                                    </div>
                                </div>
                                
                                <!-- EEG Placement Zones -->
                                <div class="placement-zone absolute" style="top: 15%; left: 35%;" data-zone="f3" data-correct="false">
                                    <div class="w-6 h-6 border-2 border-dashed border-secondary-400 rounded-full flex items-center justify-center hover:bg-secondary-400/20 transition-colors duration-300">
                                        <span class="text-xs text-secondary-400">F3</span>
                                    </div>
                                </div>
                                <div class="placement-zone absolute" style="top: 15%; right: 35%;" data-zone="f4" data-correct="false">
                                    <div class="w-6 h-6 border-2 border-dashed border-secondary-400 rounded-full flex items-center justify-center hover:bg-secondary-400/20 transition-colors duration-300">
                                        <span class="text-xs text-secondary-400">F4</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Placement Feedback -->
                        <div id="placementFeedback" class="mt-4 p-4 bg-surface-light/50 rounded-lg border border-slate-600">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-info-circle text-accent-400"></i>
                                <p class="text-sm text-text-secondary">Drag electrodes from the component palette to the correct anatomical positions on the body diagram.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Signal Analysis Results -->
                    <div class="card">
                        <h3 class="text-lg font-semibold text-white mb-4">Signal Analysis Results</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Measurement Results -->
                            <div>
                                <h4 class="text-sm font-medium text-text-secondary mb-3 uppercase tracking-wide">Measurements</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center p-3 bg-surface-light/50 rounded-lg">
                                        <span class="text-sm text-text-secondary">Heart Rate</span>
                                        <span id="measuredHR" class="text-primary-400 font-medium">72 BPM</span>
                                    </div>
                                    <div class="flex justify-between items-center p-3 bg-surface-light/50 rounded-lg">
                                        <span class="text-sm text-text-secondary">QRS Duration</span>
                                        <span id="qrsDuration" class="text-secondary-400 font-medium">0.08 s</span>
                                    </div>
                                    <div class="flex justify-between items-center p-3 bg-surface-light/50 rounded-lg">
                                        <span class="text-sm text-text-secondary">PR Interval</span>
                                        <span id="prInterval" class="text-accent-400 font-medium">0.16 s</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Interpretation -->
                            <div>
                                <h4 class="text-sm font-medium text-text-secondary mb-3 uppercase tracking-wide">Interpretation</h4>
                                <div class="space-y-3">
                                    <div class="p-3 bg-secondary-900/20 rounded-lg border border-secondary-700">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <i class="fas fa-check-circle text-secondary text-sm"></i>
                                            <span class="text-sm font-medium text-white">Normal Sinus Rhythm</span>
                                        </div>
                                        <p class="text-xs text-text-secondary">Regular rhythm with normal intervals</p>
                                    </div>
                                    <div class="p-3 bg-surface-light/50 rounded-lg border border-slate-600">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <i class="fas fa-info-circle text-accent-400 text-sm"></i>
                                            <span class="text-sm font-medium text-white">Signal Quality</span>
                                        </div>
                                        <p class="text-xs text-text-secondary">Good electrode contact, minimal artifacts</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Floating Help Button -->
    <div class="fixed bottom-6 right-6 z-50">
        <button id="helpButton" class="w-14 h-14 bg-gradient-to-br from-accent-500 to-accent-600 rounded-full shadow-dramatic hover:shadow-glow-accent transition-all duration-300 flex items-center justify-center group">
            <i class="fas fa-question text-white text-lg group-hover:scale-110 transition-transform duration-300"></i>
        </button>
    </div>

    <!-- Help Modal -->
    <div id="helpModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-surface rounded-2xl p-8 max-w-2xl w-full border border-slate-700">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold text-white">Laboratory Help</h3>
                    <button id="closeHelp" class="text-text-secondary hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <h4 class="font-medium text-white mb-2">Getting Started</h4>
                        <p class="text-sm text-text-secondary">1. Select a simulation type from the dropdown menu</p>
                        <p class="text-sm text-text-secondary">2. Drag electrodes from the component palette to the body diagram</p>
                        <p class="text-sm text-text-secondary">3. Adjust parameters using the control sliders</p>
                        <p class="text-sm text-text-secondary">4. Click Start to begin the simulation</p>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-2">Electrode Placement</h4>
                        <p class="text-sm text-text-secondary">• ECG: Place RA (right arm), LA (left arm), and LL (left leg) electrodes</p>
                        <p class="text-sm text-text-secondary">• EEG: Position frontal and parietal electrodes according to 10-20 system</p>
                        <p class="text-sm text-text-secondary">• BP: Attach cuff to upper arm for accurate readings</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Laboratory Functionality -->
    <script>
        // Global variables
        let waveformChart;
        let simulationRunning = false;
        let currentSimulationType = 'ecg';
        let placedElectrodes = [];
        let animationId;

        // Language Toggle Functionality
        const langEn = document.getElementById('langEn');
        const langAr = document.getElementById('langAr');
        const htmlElement = document.documentElement;

        langEn.addEventListener('click', () => {
            htmlElement.setAttribute('lang', 'en');
            htmlElement.setAttribute('dir', 'ltr');
            langEn.classList.add('bg-primary', 'text-white');
            langEn.classList.remove('text-text-secondary');
            langAr.classList.remove('bg-primary', 'text-white');
            langAr.classList.add('text-text-secondary');
        });

        langAr.addEventListener('click', () => {
            htmlElement.setAttribute('lang', 'ar');
            htmlElement.setAttribute('dir', 'rtl');
            langAr.classList.add('bg-primary', 'text-white');
            langAr.classList.remove('text-text-secondary');
            langEn.classList.remove('bg-primary', 'text-white');
            langEn.classList.add('text-text-secondary');
        });

        // Initialize Chart.js
        function initializeChart() {
            const ctx = document.getElementById('waveformChart').getContext('2d');
            waveformChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 100}, (_, i) => i),
                    datasets: [{
                        label: 'Signal',
                        data: [],
                        borderColor: '#60a5fa',
                        backgroundColor: 'rgba(96, 165, 250, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: true,
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    animation: {
                        duration: 0
                    }
                }
            });
        }

        // Generate waveform data based on simulation type
        function generateWaveformData() {
            const heartRate = parseInt(document.getElementById('heartRate').value);
            const amplitude = parseFloat(document.getElementById('amplitude').value);
            const noiseLevel = parseFloat(document.getElementById('noiseLevel').value);
            
            const data = [];
            const time = Date.now() / 1000;
            
            for (let i = 0; i < 100; i++) {
                let value = 0;
                
                if (currentSimulationType === 'ecg') {
                    // ECG waveform simulation
                    const t = (time + i * 0.01) * (heartRate / 60);
                    value = amplitude * Math.sin(t * 2 * Math.PI) * Math.exp(-((t % 1) - 0.2) ** 2 / 0.01);
                } else if (currentSimulationType === 'eeg') {
                    // EEG waveform simulation (alpha waves)
                    const t = time + i * 0.01;
                    value = amplitude * 0.5 * (Math.sin(t * 10 * Math.PI) + Math.sin(t * 8 * Math.PI));
                } else if (currentSimulationType === 'bp') {
                    // Blood pressure waveform
                    const t = (time + i * 0.01) * (heartRate / 60);
                    value = amplitude * (Math.sin(t * 2 * Math.PI) + 0.3 * Math.sin(t * 4 * Math.PI));
                }
                
                // Add noise
                value += (Math.random() - 0.5) * noiseLevel;
                data.push(value);
            }
            
            return data;
        }

        // Update waveform display
        function updateWaveform() {
            if (!simulationRunning) return;
            
            const data = generateWaveformData();
            waveformChart.data.datasets[0].data = data;
            waveformChart.update('none');
            
            // Update frequency display
            const heartRate = parseInt(document.getElementById('heartRate').value);
            document.getElementById('frequencyDisplay').textContent = `${(heartRate / 60).toFixed(1)} Hz`;
            
            animationId = requestAnimationFrame(updateWaveform);
        }

        // Control event listeners
        document.getElementById('startSimulation').addEventListener('click', () => {
            simulationRunning = true;
            updateWaveform();
        });

        document.getElementById('pauseSimulation').addEventListener('click', () => {
            simulationRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        });

        document.getElementById('resetSimulation').addEventListener('click', () => {
            simulationRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            waveformChart.data.datasets[0].data = [];
            waveformChart.update();
            
            // Reset electrode placements
            placedElectrodes = [];
            document.querySelectorAll('.placement-zone').forEach(zone => {
                zone.dataset.correct = 'false';
                zone.classList.remove('bg-secondary-400/20', 'border-secondary-400');
            });
            updatePlacementAccuracy();
        });

        // Parameter control listeners
        document.getElementById('heartRate').addEventListener('input', (e) => {
            document.getElementById('heartRateValue').textContent = e.target.value;
        });

        document.getElementById('amplitude').addEventListener('input', (e) => {
            document.getElementById('amplitudeValue').textContent = e.target.value;
            document.getElementById('amplitudeDisplay').textContent = `${e.target.value} mV`;
        });

        document.getElementById('noiseLevel').addEventListener('input', (e) => {
            document.getElementById('noiseLevelValue').textContent = e.target.value;
        });

        document.getElementById('simulationType').addEventListener('change', (e) => {
            currentSimulationType = e.target.value;
            
            // Update chart color based on simulation type
            let color = '#60a5fa'; // primary
            if (currentSimulationType === 'eeg') color = '#34d399'; // secondary
            if (currentSimulationType === 'bp') color = '#ef4444'; // error
            
            waveformChart.data.datasets[0].borderColor = color;
            waveformChart.data.datasets[0].backgroundColor = color + '20';
            waveformChart.update();
        });

        // Drag and drop functionality
        let draggedElement = null;

        document.querySelectorAll('.electrode-component').forEach(component => {
            component.addEventListener('dragstart', (e) => {
                draggedElement = e.target;
                e.target.style.opacity = '0.5';
            });

            component.addEventListener('dragend', (e) => {
                e.target.style.opacity = '1';
                draggedElement = null;
            });
        });

        document.querySelectorAll('.placement-zone').forEach(zone => {
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.classList.add('bg-primary-400/20');
            });

            zone.addEventListener('dragleave', (e) => {
                zone.classList.remove('bg-primary-400/20');
            });

            zone.addEventListener('drop', (e) => {
                e.preventDefault();
                zone.classList.remove('bg-primary-400/20');
                
                if (draggedElement) {
                    const electrodeType = draggedElement.dataset.type;
                    const electrodeName = draggedElement.dataset.name;
                    const zoneId = zone.dataset.zone;
                    
                    // Check if placement is correct
                    let isCorrect = false;
                    if (electrodeType === 'ecg-lead' && ['ra', 'la', 'll'].includes(zoneId)) {
                        isCorrect = true;
                    } else if (electrodeType === 'eeg-electrode' && ['f3', 'f4'].includes(zoneId)) {
                        isCorrect = true;
                    } else if (electrodeType === 'bp-cuff' && zoneId === 'arm') {
                        isCorrect = true;
                    }
                    
                    if (isCorrect) {
                        zone.dataset.correct = 'true';
                        zone.classList.add('bg-secondary-400/20', 'border-secondary-400');
                        placedElectrodes.push({zone: zoneId, electrode: electrodeName});
                        
                        // Show success feedback
                        showPlacementFeedback(`✓ ${electrodeName} correctly placed on ${zoneId.toUpperCase()}`, 'success');
                    } else {
                        showPlacementFeedback(`✗ Incorrect placement. Try again.`, 'error');
                    }
                    
                    updatePlacementAccuracy();
                }
            });
        });

        // Update placement accuracy
        function updatePlacementAccuracy() {
            const totalZones = document.querySelectorAll('.placement-zone').length;
            const correctPlacements = document.querySelectorAll('.placement-zone[data-correct="true"]').length;
            const accuracy = Math.round((correctPlacements / totalZones) * 100);
            document.getElementById('placementAccuracy').textContent = `${accuracy}%`;
        }

        // Show placement feedback
        function showPlacementFeedback(message, type) {
            const feedback = document.getElementById('placementFeedback');
            const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
            const color = type === 'success' ? 'text-secondary' : 'text-error-500';
            
            feedback.innerHTML = `
                <div class="flex items-center space-x-3">
                    <i class="fas ${icon} ${color}"></i>
                    <p class="text-sm text-text-secondary">${message}</p>
                </div>
            `;
            
            setTimeout(() => {
                feedback.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-info-circle text-accent-400"></i>
                        <p class="text-sm text-text-secondary">Drag electrodes from the component palette to the correct anatomical positions on the body diagram.</p>
                    </div>
                `;
            }, 3000);
        }

        // Help modal functionality
        document.getElementById('helpButton').addEventListener('click', () => {
            document.getElementById('helpModal').classList.remove('hidden');
        });

        document.getElementById('closeHelp').addEventListener('click', () => {
            document.getElementById('helpModal').classList.add('hidden');
        });

        document.getElementById('helpModal').addEventListener('click', (e) => {
            if (e.target === document.getElementById('helpModal')) {
                document.getElementById('helpModal').classList.add('hidden');
            }
        });

        // Save progress functionality
        document.getElementById('saveProgress').addEventListener('click', () => {
            const progress = {
                simulationType: currentSimulationType,
                placedElectrodes: placedElectrodes,
                parameters: {
                    heartRate: document.getElementById('heartRate').value,
                    amplitude: document.getElementById('amplitude').value,
                    noiseLevel: document.getElementById('noiseLevel').value
                },
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem('labProgress', JSON.stringify(progress));
            
            // Show success message
            const button = document.getElementById('saveProgress');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check mr-2"></i>Saved!';
            button.classList.add('bg-secondary', 'hover:bg-secondary-600');
            button.classList.remove('btn-secondary');
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-secondary', 'hover:bg-secondary-600');
                button.classList.add('btn-secondary');
            }, 2000);
        });

        // Initialize on page load
        window.addEventListener('load', () => {
            initializeChart();
            
            // Load saved progress if available
            const savedProgress = localStorage.getItem('labProgress');
            if (savedProgress) {
                const progress = JSON.parse(savedProgress);
                document.getElementById('simulationType').value = progress.simulationType;
                currentSimulationType = progress.simulationType;
                
                if (progress.parameters) {
                    document.getElementById('heartRate').value = progress.parameters.heartRate;
                    document.getElementById('amplitude').value = progress.parameters.amplitude;
                    document.getElementById('noiseLevel').value = progress.parameters.noiseLevel;
                    
                    // Update display values
                    document.getElementById('heartRateValue').textContent = progress.parameters.heartRate;
                    document.getElementById('amplitudeValue').textContent = progress.parameters.amplitude;
                    document.getElementById('noiseLevelValue').textContent = progress.parameters.noiseLevel;
                }
            }
        });
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
</body>
</html>