<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Simulation Laboratory - BioMed Lab Pro</title>
    <link rel="stylesheet" href="../css/main.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- PDF Export Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fbiomedlab3601back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.6"></script>
</head>
<body class="bg-background text-text-primary font-inter min-h-screen">
    <!-- Header Command Center -->
    <header class="bg-surface border-b border-slate-700 sticky top-0 z-50 backdrop-blur-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo and Brand -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-heartbeat text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gradient-primary">BioMed Lab Pro</h1>
                            <p class="text-xs text-text-secondary">Educational Technology Platform</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="dashboard_hub.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                    </a>
                    <a href="module_explorer.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-compass mr-2"></i>Explore
                    </a>
                    <a href="simulation_laboratory.html" class="text-primary-400 font-semibold border-b-2 border-primary-400 pb-1">
                        <i class="fas fa-flask mr-2"></i>Laboratory
                    </a>
                    <a href="circuit_design_workbench.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-microchip mr-2"></i>Workbench
                    </a>
                </nav>

                <!-- Bilingual Toggle and User Actions -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center bg-surface-light rounded-lg p-1">
                        <button id="langEn" class="px-3 py-1 rounded-md text-sm font-medium bg-primary text-white transition-all duration-300">
                            EN
                        </button>
                        <button id="langAr" class="px-3 py-1 rounded-md text-sm font-medium text-text-secondary hover:text-white transition-all duration-300">
                            عربي
                        </button>
                    </div>
                    <div class="w-8 h-8 bg-gradient-to-br from-accent-400 to-accent-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <button class="md:hidden text-text-secondary hover:text-white">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Laboratory Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Laboratory Header -->
        <section class="mb-8">
            <div class="bg-gradient-to-r from-primary-900/50 to-secondary-900/50 rounded-2xl p-8 border border-slate-700">
                <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between">
                    <div class="flex-1">
                        <h2 class="text-3xl font-bold text-gradient-primary mb-2">Simulation Laboratory</h2>
                        <p class="text-text-secondary text-lg mb-4">Interactive biomedical signal analysis and monitoring</p>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-secondary rounded-full animate-pulse"></div>
                                <span class="text-sm text-text-secondary">Real-time simulation active</span>
                            </div>
                            <div class="text-sm text-text-secondary">Last updated: July 18, 2025</div>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 lg:ml-8">
                        <div class="flex items-center space-x-4">
                            <button id="resetSimulation" class="btn-accent text-sm px-4 py-2">
                                <i class="fas fa-redo mr-2"></i>Reset All
                            </button>
                            <button id="saveProgress" class="btn-secondary text-sm px-4 py-2">
                                <i class="fas fa-save mr-2"></i>Save Progress
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Lab Notebook Panel (Dockable) -->
        <div id="lab-notebook-panel" class="fixed right-4 top-20 w-96 bg-surface border border-slate-700 rounded-xl shadow-dramatic z-40 transform translate-x-full transition-transform duration-300" style="max-height: calc(100vh - 6rem); overflow-y: auto;">
            <div class="p-4 border-b border-slate-700 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-book mr-2 text-primary-400"></i>
                    <span data-lang-key="notebook_title">Lab Notebook</span>
                </h3>
                <div class="flex items-center space-x-2">
                    <button id="minimize-notebook" class="p-1 text-text-secondary hover:text-white transition-colors">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button id="close-notebook" class="p-1 text-text-secondary hover:text-white transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div id="notebook-content" class="p-4 space-y-4">
                <!-- Experiment Title -->
                <div class="text-center">
                    <h4 id="notebook-experiment-title" class="text-xl font-semibold text-gradient-primary mb-2">Select an Experiment</h4>
                    <p id="notebook-experiment-status" class="text-sm text-text-secondary">Ready to begin</p>
                </div>

                <!-- Objective Section -->
                <div class="notebook-section">
                    <h5 class="text-sm font-semibold text-accent-400 mb-2 flex items-center">
                        <i class="fas fa-target mr-2"></i>
                        <span data-lang-key="objective">Objective</span>
                    </h5>
                    <p id="notebook-objective-text" class="text-sm text-text-secondary bg-surface-light/30 rounded-lg p-3">
                        Select an experiment to view its objective.
                    </p>
                </div>

                <!-- Hypothesis Section -->
                <div class="notebook-section">
                    <h5 class="text-sm font-semibold text-accent-400 mb-2 flex items-center">
                        <i class="fas fa-lightbulb mr-2"></i>
                        <span data-lang-key="hypothesis">Hypothesis</span>
                    </h5>
                    <textarea id="notebook-hypothesis-input"
                              class="w-full h-20 bg-surface-light border border-slate-600 rounded-lg p-3 text-sm text-text-primary placeholder-text-secondary resize-none focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-300"
                              placeholder="What do you expect to happen? Enter your hypothesis here..."></textarea>
                </div>

                <!-- Procedure Section -->
                <div class="notebook-section">
                    <h5 class="text-sm font-semibold text-accent-400 mb-2 flex items-center">
                        <i class="fas fa-list-check mr-2"></i>
                        <span data-lang-key="procedure">Procedure</span>
                    </h5>
                    <ul id="notebook-procedure-checklist" class="space-y-2">
                        <!-- Steps will be dynamically generated here -->
                        <li class="text-sm text-text-secondary">Start an experiment to see procedure steps</li>
                    </ul>
                </div>

                <!-- Results Section -->
                <div class="notebook-section">
                    <h5 class="text-sm font-semibold text-accent-400 mb-2 flex items-center">
                        <i class="fas fa-chart-line mr-2"></i>
                        <span data-lang-key="results">Results</span>
                    </h5>
                    <div id="notebook-results-log" class="bg-surface-light/30 rounded-lg p-3 min-h-[80px] text-sm text-text-secondary mb-3">
                        Results will appear here as you perform the experiment.
                    </div>
                    <textarea id="notebook-results-notes"
                              class="w-full h-16 bg-surface-light border border-slate-600 rounded-lg p-3 text-sm text-text-primary placeholder-text-secondary resize-none focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-300"
                              placeholder="Add notes on your results..."></textarea>
                </div>

                <!-- Analysis Section -->
                <div class="notebook-section">
                    <h5 class="text-sm font-semibold text-accent-400 mb-2 flex items-center">
                        <i class="fas fa-microscope mr-2"></i>
                        <span data-lang-key="analysis">Analysis</span>
                    </h5>
                    <textarea id="notebook-analysis-input"
                              class="w-full h-20 bg-surface-light border border-slate-600 rounded-lg p-3 text-sm text-text-primary placeholder-text-secondary resize-none focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-300"
                              placeholder="Analyze your results. What patterns do you observe?"></textarea>
                </div>

                <!-- Export Section -->
                <div class="notebook-section border-t border-slate-700 pt-4">
                    <button id="export-pdf-btn" class="w-full btn-primary text-sm py-3 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-file-pdf"></i>
                        <span>Finalize & Export Report</span>
                    </button>
                    <p class="text-xs text-text-secondary mt-2 text-center">Complete the experiment to enable export</p>
                </div>
            </div>
        </div>

        <!-- Notebook Toggle Button -->
        <button id="toggle-notebook" class="fixed right-4 top-32 bg-primary hover:bg-primary-700 text-white p-3 rounded-l-lg shadow-moderate z-30 transition-all duration-300">
            <i class="fas fa-book"></i>
        </button>

        <!-- Laboratory Interface -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Component Palette -->
            <div class="lg:col-span-1">
                <div class="card sticky top-24">
                    <h3 class="text-lg font-semibold text-white mb-4">Medical Components</h3>
                    
                    <!-- ECG Components -->
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-text-secondary mb-3 uppercase tracking-wide">ECG Electrodes</h4>
                        <div class="space-y-2">
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="ecg-lead" data-name="Lead I">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                                        <i class="fas fa-circle text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">Lead I</p>
                                        <p class="text-xs text-text-secondary">RA to LA</p>
                                    </div>
                                </div>
                            </div>
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="ecg-lead" data-name="Lead II">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                        <i class="fas fa-circle text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">Lead II</p>
                                        <p class="text-xs text-text-secondary">RA to LL</p>
                                    </div>
                                </div>
                            </div>
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="ecg-lead" data-name="Lead III">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center">
                                        <i class="fas fa-circle text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">Lead III</p>
                                        <p class="text-xs text-text-secondary">LA to LL</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- EEG Components -->
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-text-secondary mb-3 uppercase tracking-wide">EEG Electrodes</h4>
                        <div class="space-y-2">
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="eeg-electrode" data-name="Frontal">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary-400 rounded-full flex items-center justify-center">
                                        <i class="fas fa-brain text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">Frontal</p>
                                        <p class="text-xs text-text-secondary">F3, F4, Fz</p>
                                    </div>
                                </div>
                            </div>
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="eeg-electrode" data-name="Parietal">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-secondary-400 rounded-full flex items-center justify-center">
                                        <i class="fas fa-brain text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">Parietal</p>
                                        <p class="text-xs text-text-secondary">P3, P4, Pz</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- BP Components -->
                    <div>
                        <h4 class="text-sm font-medium text-text-secondary mb-3 uppercase tracking-wide">BP Monitoring</h4>
                        <div class="space-y-2">
                            <div class="electrode-component p-3 bg-surface-light/50 rounded-lg border border-slate-600 cursor-grab hover:bg-surface-light transition-colors duration-300" data-type="bp-cuff" data-name="BP Cuff">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-error-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-heartbeat text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">BP Cuff</p>
                                        <p class="text-xs text-text-secondary">Arm placement</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Simulation Canvas -->
            <div class="lg:col-span-3">
                <div class="space-y-6">
                    <!-- Simulation Controls -->
                    <div class="card">
                        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-white mb-4 sm:mb-0">Simulation Controls</h3>
                            <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                                <div class="flex items-center space-x-2">
                                    <label class="text-sm text-text-secondary">Experiment:</label>
                                    <select id="experimentType" class="input-field text-sm py-2">
                                        <option value="">Select Experiment</option>
                                        <option value="ecg_lead_ii_monitoring">ECG Lead II Monitoring</option>
                                        <option value="blood_pressure_measurement">Blood Pressure Measurement</option>
                                        <option value="eeg_alpha_wave_detection">EEG Alpha Wave Detection</option>
                                    </select>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <label class="text-sm text-text-secondary">Patient:</label>
                                    <select id="patientProfile" class="input-field text-sm py-2">
                                        <option value="">Select Patient</option>
                                        <option value="p01_healthy">Healthy Adult (25 y/o)</option>
                                        <option value="p02_athlete">Athlete (28 y/o)</option>
                                        <option value="p03_elderly">Elderly Patient (75 y/o)</option>
                                        <option value="p04_hypertensive">Hypertensive (55 y/o)</option>
                                    </select>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button id="startExperiment" class="btn-primary text-sm px-4 py-2" disabled>
                                        <i class="fas fa-play mr-2"></i>Start Experiment
                                    </button>
                                    <button id="pauseSimulation" class="btn-accent text-sm px-4 py-2">
                                        <i class="fas fa-pause mr-2"></i>Pause
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Parameter Controls -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm text-text-secondary mb-2">Heart Rate (BPM)</label>
                                <input type="range" id="heartRate" min="40" max="180" value="72" class="w-full h-2 bg-surface-light rounded-lg appearance-none cursor-pointer" />
                                <div class="flex justify-between text-xs text-text-secondary mt-1">
                                    <span>40</span>
                                    <span id="heartRateValue" class="text-primary-400 font-medium">72</span>
                                    <span>180</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm text-text-secondary mb-2">Amplitude</label>
                                <input type="range" id="amplitude" min="0.5" max="2.0" step="0.1" value="1.0" class="w-full h-2 bg-surface-light rounded-lg appearance-none cursor-pointer" />
                                <div class="flex justify-between text-xs text-text-secondary mt-1">
                                    <span>0.5</span>
                                    <span id="amplitudeValue" class="text-secondary-400 font-medium">1.0</span>
                                    <span>2.0</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm text-text-secondary mb-2">Noise Level</label>
                                <input type="range" id="noiseLevel" min="0" max="0.3" step="0.05" value="0.1" class="w-full h-2 bg-surface-light rounded-lg appearance-none cursor-pointer" />
                                <div class="flex justify-between text-xs text-text-secondary mt-1">
                                    <span>0</span>
                                    <span id="noiseLevelValue" class="text-accent-400 font-medium">0.1</span>
                                    <span>0.3</span>
                                </div>
                            </div>
                        </div>

                        <!-- Artifact Injection Controls -->
                        <div class="mt-6 p-4 bg-surface-light/30 rounded-lg border border-slate-600">
                            <h4 class="text-sm font-semibold text-accent-400 mb-3 flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                Artifact Injection Controls
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm text-text-secondary mb-2">Baseline Wander</label>
                                    <input type="range" id="baselineWander" min="0" max="1" step="0.1" value="0" class="w-full h-2 bg-surface-light rounded-lg appearance-none cursor-pointer" />
                                    <div class="flex justify-between text-xs text-text-secondary mt-1">
                                        <span>None</span>
                                        <span id="baselineWanderValue" class="text-warning-400 font-medium">0</span>
                                        <span>High</span>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm text-text-secondary mb-2">EMG Artifact</label>
                                    <input type="range" id="emgArtifact" min="0" max="1" step="0.1" value="0" class="w-full h-2 bg-surface-light rounded-lg appearance-none cursor-pointer" />
                                    <div class="flex justify-between text-xs text-text-secondary mt-1">
                                        <span>None</span>
                                        <span id="emgArtifactValue" class="text-warning-400 font-medium">0</span>
                                        <span>High</span>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm text-text-secondary mb-2">50Hz Interference</label>
                                    <input type="range" id="powerlineInterference" min="0" max="1" step="0.1" value="0" class="w-full h-2 bg-surface-light rounded-lg appearance-none cursor-pointer" />
                                    <div class="flex justify-between text-xs text-text-secondary mt-1">
                                        <span>None</span>
                                        <span id="powerlineInterferenceValue" class="text-warning-400 font-medium">0</span>
                                        <span>High</span>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4 flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button id="injectRandomArtifacts" class="btn-accent text-sm px-4 py-2">
                                        <i class="fas fa-random mr-2"></i>Random Artifacts
                                    </button>
                                    <button id="clearAllArtifacts" class="btn-secondary text-sm px-4 py-2">
                                        <i class="fas fa-broom mr-2"></i>Clear All
                                    </button>
                                </div>
                                <div class="text-sm">
                                    <span class="text-text-secondary">Signal Quality: </span>
                                    <span id="signalQuality" class="font-medium text-secondary-400">Excellent</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Waveform Display -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-white">Real-time Waveform</h3>
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-secondary rounded-full animate-pulse"></div>
                                <span class="text-sm text-text-secondary">Live Signal</span>
                            </div>
                        </div>
                        <div class="relative">
                            <canvas id="waveformChart" class="w-full h-64 bg-surface-light/30 rounded-lg"></canvas>
                            <div class="absolute top-4 left-4 bg-surface/80 rounded-lg p-2">
                                <div class="text-xs text-text-secondary">
                                    <div>Frequency: <span id="frequencyDisplay" class="text-primary-400">1.2 Hz</span></div>
                                    <div>Amplitude: <span id="amplitudeDisplay" class="text-secondary-400">1.0 mV</span></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Electrode Placement Game -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-white">Electrode Placement Training</h3>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-text-secondary">Accuracy:</span>
                                <span id="placementAccuracy" class="text-accent-400 font-medium">0%</span>
                            </div>
                        </div>
                        
                        <!-- Human Body Diagram -->
                        <div class="relative bg-surface-light/30 rounded-lg p-8 min-h-96">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <img src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Human Body Diagram" class="w-48 h-80 object-contain opacity-60" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                            </div>
                            
                            <!-- Placement Zones -->
                            <div id="placementZones" class="relative z-10">
                                <!-- ECG Placement Zones -->
                                <div class="placement-zone absolute" style="top: 25%; left: 20%;" data-zone="ra" data-correct="false">
                                    <div class="w-8 h-8 border-2 border-dashed border-primary-400 rounded-full flex items-center justify-center hover:bg-primary-400/20 transition-colors duration-300">
                                        <span class="text-xs text-primary-400">RA</span>
                                    </div>
                                </div>
                                <div class="placement-zone absolute" style="top: 25%; right: 20%;" data-zone="la" data-correct="false">
                                    <div class="w-8 h-8 border-2 border-dashed border-primary-400 rounded-full flex items-center justify-center hover:bg-primary-400/20 transition-colors duration-300">
                                        <span class="text-xs text-primary-400">LA</span>
                                    </div>
                                </div>
                                <div class="placement-zone absolute" style="bottom: 25%; left: 45%;" data-zone="ll" data-correct="false">
                                    <div class="w-8 h-8 border-2 border-dashed border-primary-400 rounded-full flex items-center justify-center hover:bg-primary-400/20 transition-colors duration-300">
                                        <span class="text-xs text-primary-400">LL</span>
                                    </div>
                                </div>
                                
                                <!-- EEG Placement Zones -->
                                <div class="placement-zone absolute" style="top: 15%; left: 35%;" data-zone="f3" data-correct="false">
                                    <div class="w-6 h-6 border-2 border-dashed border-secondary-400 rounded-full flex items-center justify-center hover:bg-secondary-400/20 transition-colors duration-300">
                                        <span class="text-xs text-secondary-400">F3</span>
                                    </div>
                                </div>
                                <div class="placement-zone absolute" style="top: 15%; right: 35%;" data-zone="f4" data-correct="false">
                                    <div class="w-6 h-6 border-2 border-dashed border-secondary-400 rounded-full flex items-center justify-center hover:bg-secondary-400/20 transition-colors duration-300">
                                        <span class="text-xs text-secondary-400">F4</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Placement Feedback -->
                        <div id="placementFeedback" class="mt-4 p-4 bg-surface-light/50 rounded-lg border border-slate-600">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-info-circle text-accent-400"></i>
                                <p class="text-sm text-text-secondary">Drag electrodes from the component palette to the correct anatomical positions on the body diagram.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Signal Analysis Results -->
                    <div class="card">
                        <h3 class="text-lg font-semibold text-white mb-4">Signal Analysis Results</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Measurement Results -->
                            <div>
                                <h4 class="text-sm font-medium text-text-secondary mb-3 uppercase tracking-wide">Measurements</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center p-3 bg-surface-light/50 rounded-lg">
                                        <span class="text-sm text-text-secondary">Heart Rate</span>
                                        <span id="measuredHR" class="text-primary-400 font-medium">72 BPM</span>
                                    </div>
                                    <div class="flex justify-between items-center p-3 bg-surface-light/50 rounded-lg">
                                        <span class="text-sm text-text-secondary">QRS Duration</span>
                                        <span id="qrsDuration" class="text-secondary-400 font-medium">0.08 s</span>
                                    </div>
                                    <div class="flex justify-between items-center p-3 bg-surface-light/50 rounded-lg">
                                        <span class="text-sm text-text-secondary">PR Interval</span>
                                        <span id="prInterval" class="text-accent-400 font-medium">0.16 s</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Interpretation -->
                            <div>
                                <h4 class="text-sm font-medium text-text-secondary mb-3 uppercase tracking-wide">Interpretation</h4>
                                <div class="space-y-3">
                                    <div class="p-3 bg-secondary-900/20 rounded-lg border border-secondary-700">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <i class="fas fa-check-circle text-secondary text-sm"></i>
                                            <span class="text-sm font-medium text-white">Normal Sinus Rhythm</span>
                                        </div>
                                        <p class="text-xs text-text-secondary">Regular rhythm with normal intervals</p>
                                    </div>
                                    <div class="p-3 bg-surface-light/50 rounded-lg border border-slate-600">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <i class="fas fa-info-circle text-accent-400 text-sm"></i>
                                            <span class="text-sm font-medium text-white">Signal Quality</span>
                                        </div>
                                        <p class="text-xs text-text-secondary">Good electrode contact, minimal artifacts</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Floating Help Button -->
    <div class="fixed bottom-6 right-6 z-50">
        <button id="helpButton" class="w-14 h-14 bg-gradient-to-br from-accent-500 to-accent-600 rounded-full shadow-dramatic hover:shadow-glow-accent transition-all duration-300 flex items-center justify-center group">
            <i class="fas fa-question text-white text-lg group-hover:scale-110 transition-transform duration-300"></i>
        </button>
    </div>

    <!-- Help Modal -->
    <div id="helpModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-surface rounded-2xl p-8 max-w-2xl w-full border border-slate-700">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold text-white">Laboratory Help</h3>
                    <button id="closeHelp" class="text-text-secondary hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <h4 class="font-medium text-white mb-2">Getting Started</h4>
                        <p class="text-sm text-text-secondary">1. Select a simulation type from the dropdown menu</p>
                        <p class="text-sm text-text-secondary">2. Drag electrodes from the component palette to the body diagram</p>
                        <p class="text-sm text-text-secondary">3. Adjust parameters using the control sliders</p>
                        <p class="text-sm text-text-secondary">4. Click Start to begin the simulation</p>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-2">Electrode Placement</h4>
                        <p class="text-sm text-text-secondary">• ECG: Place RA (right arm), LA (left arm), and LL (left leg) electrodes</p>
                        <p class="text-sm text-text-secondary">• EEG: Position frontal and parietal electrodes according to 10-20 system</p>
                        <p class="text-sm text-text-secondary">• BP: Attach cuff to upper arm for accurate readings</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Laboratory Functionality -->
    <script>
        // Global variables
        let waveformChart;
        let simulationRunning = false;
        let currentSimulationType = 'ecg';
        let placedElectrodes = [];
        let animationId;
        let labNotebook; // Lab Notebook Manager instance
        let currentPatientProfile = null;
        let audioManager;

        // Lab Notebook Manager Class
        class LabNotebookManager {
            constructor() {
                this.currentExperiment = null;
                this.isNotebookVisible = false;
                this.setupEventListeners();
                this.loadSavedExperiment();
            }

            setupEventListeners() {
                // Custom event listeners for experiment tracking
                document.addEventListener('experimentStarted', (e) => this.startExperiment(e.detail));
                document.addEventListener('procedureStepCompleted', (e) => this.completeStep(e.detail));
                document.addEventListener('resultObtained', (e) => this.logResult(e.detail));

                // UI event listeners
                document.getElementById('toggle-notebook').addEventListener('click', () => this.toggleNotebook());
                document.getElementById('close-notebook').addEventListener('click', () => this.hideNotebook());
                document.getElementById('minimize-notebook').addEventListener('click', () => this.minimizeNotebook());
                document.getElementById('export-pdf-btn').addEventListener('click', () => this.exportToPDF());

                // Auto-save on text changes
                ['notebook-hypothesis-input', 'notebook-results-notes', 'notebook-analysis-input'].forEach(id => {
                    document.getElementById(id).addEventListener('input', () => this.saveState());
                });
            }

            startExperiment(experimentData) {
                this.currentExperiment = {
                    experimentId: experimentData.id,
                    title: experimentData.title,
                    objective: experimentData.objective,
                    startTime: new Date().toISOString(),
                    status: 'in-progress',
                    hypothesis: '',
                    procedure: experimentData.procedure || {},
                    results: {
                        waveform_image_b64: null,
                        measurements: [],
                        notes: ''
                    },
                    analysis: ''
                };

                this.updateNotebookUI();
                this.showNotebook();
                this.saveState();
            }

            completeStep(stepData) {
                if (!this.currentExperiment) return;

                const { stepId } = stepData;
                if (this.currentExperiment.procedure[stepId]) {
                    this.currentExperiment.procedure[stepId].completed = true;
                    this.currentExperiment.procedure[stepId].timestamp = new Date().toISOString();
                    this.updateChecklistUI(stepId);
                    this.saveState();
                }
            }

            logResult(resultData) {
                if (!this.currentExperiment) return;

                const { resultType, data, description } = resultData;
                const timestamp = new Date().toISOString();

                const result = {
                    type: resultType,
                    data: data,
                    description: description || this.formatResultDescription(resultType, data),
                    timestamp: timestamp
                };

                this.currentExperiment.results.measurements.push(result);
                this.updateResultsUI(result);
                this.saveState();
            }

            formatResultDescription(type, data) {
                switch (type) {
                    case 'heart_rate':
                        return `Heart Rate: ${data.value} ${data.unit || 'bpm'}`;
                    case 'blood_pressure':
                        return `Blood Pressure: ${data.systolic}/${data.diastolic} ${data.unit || 'mmHg'}`;
                    case 'signal_quality':
                        return `Signal Quality: ${data.snr} dB SNR`;
                    case 'electrode_placement':
                        return `Electrode placed: ${data.position} (${data.lead})`;
                    case 'patient_profile':
                        return `Patient Profile: ${data.name}`;
                    default:
                        return `${type}: ${JSON.stringify(data)}`;
                }
            }

            updateNotebookUI() {
                if (!this.currentExperiment) return;

                document.getElementById('notebook-experiment-title').textContent = this.currentExperiment.title;
                document.getElementById('notebook-experiment-status').textContent =
                    this.currentExperiment.status === 'in-progress' ? 'Experiment in progress...' : 'Experiment completed';
                document.getElementById('notebook-objective-text').textContent = this.currentExperiment.objective;

                this.updateProcedureUI();
                this.updateResultsUI();
            }

            updateProcedureUI() {
                const checklist = document.getElementById('notebook-procedure-checklist');
                checklist.innerHTML = '';

                Object.entries(this.currentExperiment.procedure).forEach(([stepId, step]) => {
                    const li = document.createElement('li');
                    li.className = `flex items-center space-x-2 text-sm ${step.completed ? 'text-secondary-400' : 'text-text-secondary'}`;
                    li.dataset.stepId = stepId;

                    li.innerHTML = `
                        <i class="fas ${step.completed ? 'fa-check-circle text-secondary-400' : 'fa-circle text-slate-600'}"></i>
                        <span>${step.description || stepId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                        ${step.completed ? `<span class="text-xs text-text-muted ml-auto">${new Date(step.timestamp).toLocaleTimeString()}</span>` : ''}
                    `;

                    checklist.appendChild(li);
                });
            }

            updateChecklistUI(stepId) {
                const stepElement = document.querySelector(`[data-step-id="${stepId}"]`);
                if (stepElement) {
                    stepElement.className = 'flex items-center space-x-2 text-sm text-secondary-400';
                    const icon = stepElement.querySelector('i');
                    icon.className = 'fas fa-check-circle text-secondary-400';

                    const timestamp = document.createElement('span');
                    timestamp.className = 'text-xs text-text-muted ml-auto';
                    timestamp.textContent = new Date().toLocaleTimeString();
                    stepElement.appendChild(timestamp);
                }
            }

            updateResultsUI(newResult = null) {
                const resultsLog = document.getElementById('notebook-results-log');

                if (newResult) {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'mb-2 p-2 bg-surface-light/50 rounded border-l-2 border-primary-400';
                    resultDiv.innerHTML = `
                        <div class="flex justify-between items-start">
                            <span class="text-sm text-white">${newResult.description}</span>
                            <span class="text-xs text-text-muted">${new Date(newResult.timestamp).toLocaleTimeString()}</span>
                        </div>
                    `;
                    resultsLog.appendChild(resultDiv);
                    resultsLog.scrollTop = resultsLog.scrollHeight;
                } else if (this.currentExperiment && this.currentExperiment.results.measurements.length > 0) {
                    resultsLog.innerHTML = '';
                    this.currentExperiment.results.measurements.forEach(result => {
                        this.updateResultsUI(result);
                    });
                }
            }

            toggleNotebook() {
                this.isNotebookVisible ? this.hideNotebook() : this.showNotebook();
            }

            showNotebook() {
                const panel = document.getElementById('lab-notebook-panel');
                panel.classList.remove('translate-x-full');
                this.isNotebookVisible = true;
            }

            hideNotebook() {
                const panel = document.getElementById('lab-notebook-panel');
                panel.classList.add('translate-x-full');
                this.isNotebookVisible = false;
            }

            minimizeNotebook() {
                const content = document.getElementById('notebook-content');
                content.classList.toggle('hidden');
            }

            saveState() {
                if (!this.currentExperiment) return;

                // Update experiment data with current form values
                this.currentExperiment.hypothesis = document.getElementById('notebook-hypothesis-input').value;
                this.currentExperiment.results.notes = document.getElementById('notebook-results-notes').value;
                this.currentExperiment.analysis = document.getElementById('notebook-analysis-input').value;

                // Save to localStorage
                const key = `biomed_lab_pro_experiment_${this.currentExperiment.experimentId}`;
                localStorage.setItem(key, JSON.stringify(this.currentExperiment));
            }

            loadSavedExperiment() {
                // Check for any in-progress experiments
                const keys = Object.keys(localStorage).filter(key => key.startsWith('biomed_lab_pro_experiment_'));

                for (const key of keys) {
                    try {
                        const experiment = JSON.parse(localStorage.getItem(key));
                        if (experiment.status === 'in-progress') {
                            this.currentExperiment = experiment;
                            this.updateNotebookUI();

                            // Restore form values
                            document.getElementById('notebook-hypothesis-input').value = experiment.hypothesis || '';
                            document.getElementById('notebook-results-notes').value = experiment.results.notes || '';
                            document.getElementById('notebook-analysis-input').value = experiment.analysis || '';

                            // Show notification about resumed experiment
                            this.showNotification(`Resumed experiment: ${experiment.title}`, 'info');
                            break;
                        }
                    } catch (e) {
                        console.warn('Failed to load saved experiment:', e);
                    }
                }
            }

            async exportToPDF() {
                if (!this.currentExperiment) {
                    this.showNotification('No experiment data to export', 'warning');
                    return;
                }

                try {
                    // Update experiment status
                    this.currentExperiment.status = 'completed';
                    this.currentExperiment.completedTime = new Date().toISOString();
                    this.saveState();

                    // Capture waveform chart
                    const canvas = document.getElementById('waveformChart');
                    const chartImage = canvas.toDataURL('image/png');

                    // Create PDF
                    const { jsPDF } = window.jspdf;
                    const doc = new jsPDF();

                    // Add header
                    doc.setFontSize(20);
                    doc.text('BioMed Lab Pro - Laboratory Report', 20, 20);

                    doc.setFontSize(12);
                    doc.text(`Experiment: ${this.currentExperiment.title}`, 20, 35);
                    doc.text(`Date: ${new Date(this.currentExperiment.startTime).toLocaleDateString()}`, 20, 45);
                    doc.text(`Duration: ${this.calculateDuration()}`, 20, 55);

                    // Add sections
                    let yPos = 70;

                    // Objective
                    doc.setFontSize(14);
                    doc.text('Objective:', 20, yPos);
                    yPos += 10;
                    doc.setFontSize(10);
                    const objectiveLines = doc.splitTextToSize(this.currentExperiment.objective, 170);
                    doc.text(objectiveLines, 20, yPos);
                    yPos += objectiveLines.length * 5 + 10;

                    // Hypothesis
                    if (this.currentExperiment.hypothesis) {
                        doc.setFontSize(14);
                        doc.text('Hypothesis:', 20, yPos);
                        yPos += 10;
                        doc.setFontSize(10);
                        const hypothesisLines = doc.splitTextToSize(this.currentExperiment.hypothesis, 170);
                        doc.text(hypothesisLines, 20, yPos);
                        yPos += hypothesisLines.length * 5 + 10;
                    }

                    // Results
                    doc.setFontSize(14);
                    doc.text('Results:', 20, yPos);
                    yPos += 10;

                    this.currentExperiment.results.measurements.forEach(result => {
                        doc.setFontSize(10);
                        doc.text(`• ${result.description}`, 25, yPos);
                        yPos += 5;
                    });
                    yPos += 10;

                    // Add chart image if available
                    if (chartImage && yPos < 200) {
                        doc.addImage(chartImage, 'PNG', 20, yPos, 170, 80);
                        yPos += 90;
                    }

                    // Analysis
                    if (this.currentExperiment.analysis && yPos < 250) {
                        doc.setFontSize(14);
                        doc.text('Analysis:', 20, yPos);
                        yPos += 10;
                        doc.setFontSize(10);
                        const analysisLines = doc.splitTextToSize(this.currentExperiment.analysis, 170);
                        doc.text(analysisLines, 20, yPos);
                    }

                    // Save PDF
                    const filename = `BioMed_Lab_Report_${this.currentExperiment.experimentId}_${new Date().toISOString().split('T')[0]}.pdf`;
                    doc.save(filename);

                    this.showNotification('Lab report exported successfully!', 'success');
                } catch (error) {
                    console.error('PDF export failed:', error);
                    this.showNotification('Failed to export PDF. Please try again.', 'error');
                }
            }

            calculateDuration() {
                if (!this.currentExperiment.startTime) return 'Unknown';

                const start = new Date(this.currentExperiment.startTime);
                const end = this.currentExperiment.completedTime ?
                    new Date(this.currentExperiment.completedTime) : new Date();

                const diffMs = end - start;
                const diffMins = Math.floor(diffMs / 60000);
                const diffSecs = Math.floor((diffMs % 60000) / 1000);

                return `${diffMins}m ${diffSecs}s`;
            }

            showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

                const colors = {
                    success: 'bg-secondary text-white',
                    error: 'bg-error text-white',
                    warning: 'bg-warning text-black',
                    info: 'bg-primary text-white'
                };

                notification.className += ` ${colors[type] || colors.info}`;
                notification.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <i class="fas ${type === 'success' ? 'fa-check' : type === 'error' ? 'fa-exclamation-triangle' : 'fa-info'}"></i>
                        <span>${message}</span>
                    </div>
                `;

                document.body.appendChild(notification);

                // Animate in
                setTimeout(() => notification.classList.remove('translate-x-full'), 100);

                // Remove after 3 seconds
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 3000);
            }
        }

        // Patient Profiles for High-Fidelity Simulation
        const patientProfiles = {
            "p01_healthy": {
                name: "Healthy Adult (25 y/o)",
                baseHR: 70,
                signalNoise: 0.02,
                artifacts: { baseline: 0.1, emg: 0.05, powerline: 0.03 },
                bloodPressure: { systolic: 120, diastolic: 80 }
            },
            "p02_athlete": {
                name: "Athlete (28 y/o)",
                baseHR: 52,
                signalNoise: 0.01,
                artifacts: { baseline: 0.05, emg: 0.02, powerline: 0.02 },
                bloodPressure: { systolic: 110, diastolic: 70 }
            },
            "p03_elderly": {
                name: "Elderly Patient (75 y/o)",
                baseHR: 85,
                signalNoise: 0.15,
                artifacts: { baseline: 0.3, emg: 0.2, powerline: 0.1 },
                bloodPressure: { systolic: 140, diastolic: 90 }
            },
            "p04_hypertensive": {
                name: "Hypertensive Patient (55 y/o)",
                baseHR: 88,
                signalNoise: 0.08,
                artifacts: { baseline: 0.2, emg: 0.1, powerline: 0.05 },
                bloodPressure: { systolic: 160, diastolic: 100 }
            }
        };

        // Experiment Definitions
        const experimentDefinitions = {
            "ecg_lead_ii_monitoring": {
                id: "ecg_lead_ii_monitoring",
                title: "ECG Lead II Monitoring",
                objective: "Learn to properly place electrodes for Lead II ECG monitoring and analyze normal cardiac rhythm patterns. Understand the electrical conduction system of the heart and identify key waveform components (P, QRS, T waves).",
                procedure: {
                    "select_patient": { description: "Select patient profile", completed: false },
                    "place_ra_electrode": { description: "Place Right Arm (RA) electrode", completed: false },
                    "place_la_electrode": { description: "Place Left Arm (LA) electrode", completed: false },
                    "place_ll_electrode": { description: "Place Left Leg (LL) electrode", completed: false },
                    "select_lead_ii": { description: "Configure for Lead II monitoring", completed: false },
                    "start_monitoring": { description: "Begin ECG monitoring", completed: false },
                    "analyze_rhythm": { description: "Analyze cardiac rhythm", completed: false }
                }
            },
            "blood_pressure_measurement": {
                id: "blood_pressure_measurement",
                title: "Non-invasive Blood Pressure Measurement",
                objective: "Master the technique of automated blood pressure measurement using oscillometric method. Understand the relationship between systolic and diastolic pressures and their clinical significance.",
                procedure: {
                    "select_patient": { description: "Select patient profile", completed: false },
                    "position_cuff": { description: "Position blood pressure cuff", completed: false },
                    "calibrate_system": { description: "Calibrate measurement system", completed: false },
                    "initiate_measurement": { description: "Start blood pressure measurement", completed: false },
                    "record_values": { description: "Record systolic/diastolic values", completed: false },
                    "interpret_results": { description: "Interpret clinical significance", completed: false }
                }
            },
            "eeg_alpha_wave_detection": {
                id: "eeg_alpha_wave_detection",
                title: "EEG Alpha Wave Detection",
                objective: "Learn to detect and analyze alpha wave patterns in EEG recordings. Understand the neurophysiological basis of alpha rhythms and their clinical applications in assessing brain function.",
                procedure: {
                    "select_patient": { description: "Select patient profile", completed: false },
                    "place_electrodes": { description: "Place EEG electrodes (10-20 system)", completed: false },
                    "set_filters": { description: "Configure frequency filters (8-13 Hz)", completed: false },
                    "record_baseline": { description: "Record eyes-closed baseline", completed: false },
                    "detect_alpha": { description: "Identify alpha wave patterns", completed: false },
                    "measure_frequency": { description: "Measure dominant alpha frequency", completed: false }
                }
            }
        };

        // Audio Manager for Enhanced Feedback
        class AudioManager {
            constructor() {
                this.audioContext = null;
                this.sounds = {};
                this.enabled = true;
                this.initializeAudio();
            }

            async initializeAudio() {
                try {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.generateSounds();
                } catch (error) {
                    console.warn('Audio not available:', error);
                    this.enabled = false;
                }
            }

            generateSounds() {
                // Generate procedural audio for different events
                this.sounds = {
                    'electrode_snap': this.createTone(800, 0.1, 'sine'),
                    'measurement_complete': this.createTone(600, 0.2, 'sine'),
                    'alarm_tachy': this.createTone(1000, 0.5, 'square'),
                    'alarm_brady': this.createTone(400, 0.5, 'square'),
                    'step_complete': this.createTone(700, 0.15, 'triangle')
                };
            }

            createTone(frequency, duration, waveType = 'sine') {
                return () => {
                    if (!this.enabled || !this.audioContext) return;

                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(this.audioContext.destination);

                    oscillator.frequency.value = frequency;
                    oscillator.type = waveType;

                    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

                    oscillator.start(this.audioContext.currentTime);
                    oscillator.stop(this.audioContext.currentTime + duration);
                };
            }

            play(soundName) {
                if (this.sounds[soundName]) {
                    this.sounds[soundName]();
                }
            }

            setEnabled(enabled) {
                this.enabled = enabled;
            }
        }

        // Language Toggle Functionality
        const langEn = document.getElementById('langEn');
        const langAr = document.getElementById('langAr');
        const htmlElement = document.documentElement;

        langEn.addEventListener('click', () => {
            htmlElement.setAttribute('lang', 'en');
            htmlElement.setAttribute('dir', 'ltr');
            langEn.classList.add('bg-primary', 'text-white');
            langEn.classList.remove('text-text-secondary');
            langAr.classList.remove('bg-primary', 'text-white');
            langAr.classList.add('text-text-secondary');
        });

        langAr.addEventListener('click', () => {
            htmlElement.setAttribute('lang', 'ar');
            htmlElement.setAttribute('dir', 'rtl');
            langAr.classList.add('bg-primary', 'text-white');
            langAr.classList.remove('text-text-secondary');
            langEn.classList.remove('bg-primary', 'text-white');
            langEn.classList.add('text-text-secondary');
        });

        // Initialize Chart.js
        function initializeChart() {
            const ctx = document.getElementById('waveformChart').getContext('2d');
            waveformChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 100}, (_, i) => i),
                    datasets: [{
                        label: 'Signal',
                        data: [],
                        borderColor: '#60a5fa',
                        backgroundColor: 'rgba(96, 165, 250, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: true,
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    animation: {
                        duration: 0
                    }
                }
            });
        }

        // Enhanced waveform generation with patient profile integration
        function generateWaveformData() {
            // Use patient profile data if available, otherwise fall back to UI controls
            const heartRate = currentPatientProfile ?
                currentPatientProfile.baseHR :
                parseInt(document.getElementById('heartRate').value);
            const amplitude = parseFloat(document.getElementById('amplitude').value);
            const baseNoiseLevel = currentPatientProfile ?
                currentPatientProfile.signalNoise :
                parseFloat(document.getElementById('noiseLevel').value);

            const data = [];
            const time = Date.now() / 1000;

            for (let i = 0; i < 100; i++) {
                let value = 0;

                if (currentSimulationType === 'ecg') {
                    // Enhanced ECG waveform simulation
                    const t = (time + i * 0.01) * (heartRate / 60);
                    const phase = t % 1;

                    // P wave
                    if (phase >= 0.0 && phase <= 0.12) {
                        value += amplitude * 0.2 * Math.sin((phase / 0.12) * Math.PI);
                    }
                    // QRS complex
                    else if (phase >= 0.2 && phase <= 0.32) {
                        const qrsPhase = (phase - 0.2) / 0.12;
                        value += amplitude * Math.sin(qrsPhase * Math.PI) * (1 + 0.5 * Math.sin(qrsPhase * 3 * Math.PI));
                    }
                    // T wave
                    else if (phase >= 0.4 && phase <= 0.6) {
                        value += amplitude * 0.3 * Math.sin(((phase - 0.4) / 0.2) * Math.PI);
                    }

                } else if (currentSimulationType === 'eeg') {
                    // Enhanced EEG simulation
                    const t = time + i * 0.01;
                    value += amplitude * 0.6 * Math.sin(t * 10 * Math.PI); // Alpha waves
                    value += amplitude * 0.3 * Math.sin(t * 20 * Math.PI); // Beta waves
                    value += amplitude * 0.2 * Math.sin(t * 6 * Math.PI);  // Theta waves

                } else if (currentSimulationType === 'bp') {
                    // Enhanced blood pressure waveform
                    const t = (time + i * 0.01) * (heartRate / 60);
                    const phase = t % 1;

                    if (phase <= 0.3) {
                        value = amplitude * (Math.sin((phase / 0.3) * Math.PI * 0.5) ** 2);
                    } else {
                        const diastolicPhase = (phase - 0.3) / 0.7;
                        value = amplitude * Math.exp(-diastolicPhase * 3);
                    }
                }

                // Add artifacts (patient profile + manual injection)
                const baselineLevel = currentPatientProfile ? currentPatientProfile.artifacts.baseline : 0;
                const emgLevel = currentPatientProfile ? currentPatientProfile.artifacts.emg : 0;
                const powerlineLevel = currentPatientProfile ? currentPatientProfile.artifacts.powerline : 0;

                // Manual artifact injection (from UI controls)
                const manualBaseline = parseFloat(document.getElementById('baselineWander').value);
                const manualEmg = parseFloat(document.getElementById('emgArtifact').value);
                const manualPowerline = parseFloat(document.getElementById('powerlineInterference').value);

                // Combine patient and manual artifacts
                const totalBaseline = Math.max(baselineLevel, manualBaseline);
                const totalEmg = Math.max(emgLevel, manualEmg);
                const totalPowerline = Math.max(powerlineLevel, manualPowerline);

                // Apply artifacts
                if (totalBaseline > 0) {
                    value += amplitude * totalBaseline * Math.sin(time * 0.5 + i * 0.1);
                }
                if (totalEmg > 0) {
                    value += amplitude * totalEmg * (Math.random() - 0.5) * Math.sin(time * 50 + i);
                }
                if (totalPowerline > 0) {
                    value += amplitude * totalPowerline *
                            Math.sin(time * 2 * Math.PI * 60 + i * 0.01 * 2 * Math.PI * 60);
                }

                // Add realistic noise
                const noiseLevel = baseNoiseLevel * amplitude;
                value += (Math.random() - 0.5) * noiseLevel;
                data.push(value);
            }

            return data;
        }

        // Update waveform display
        function updateWaveform() {
            if (!simulationRunning) return;
            
            const data = generateWaveformData();
            waveformChart.data.datasets[0].data = data;
            waveformChart.update('none');
            
            // Update frequency display
            const heartRate = parseInt(document.getElementById('heartRate').value);
            document.getElementById('frequencyDisplay').textContent = `${(heartRate / 60).toFixed(1)} Hz`;
            
            animationId = requestAnimationFrame(updateWaveform);
        }

        // Initialize Lab Notebook and Audio Manager
        labNotebook = new LabNotebookManager();
        audioManager = new AudioManager();

        // Enhanced Control event listeners
        document.getElementById('experimentType').addEventListener('change', (e) => {
            const experimentId = e.target.value;
            const startBtn = document.getElementById('startExperiment');
            const patientSelect = document.getElementById('patientProfile');

            if (experimentId && patientSelect.value) {
                startBtn.disabled = false;
            } else {
                startBtn.disabled = true;
            }
        });

        document.getElementById('patientProfile').addEventListener('change', (e) => {
            const patientId = e.target.value;
            const experimentSelect = document.getElementById('experimentType');
            const startBtn = document.getElementById('startExperiment');

            if (patientId) {
                currentPatientProfile = patientProfiles[patientId];

                // Dispatch patient selection event
                document.dispatchEvent(new CustomEvent('resultObtained', {
                    detail: {
                        resultType: 'patient_profile',
                        data: { name: currentPatientProfile.name, id: patientId },
                        description: `Patient Profile Selected: ${currentPatientProfile.name}`
                    }
                }));

                audioManager.play('step_complete');
            }

            if (patientId && experimentSelect.value) {
                startBtn.disabled = false;
            } else {
                startBtn.disabled = true;
            }
        });

        document.getElementById('startExperiment').addEventListener('click', () => {
            const experimentId = document.getElementById('experimentType').value;
            const patientId = document.getElementById('patientProfile').value;

            if (!experimentId || !patientId) {
                labNotebook.showNotification('Please select both experiment and patient profile', 'warning');
                return;
            }

            // Start the experiment in the notebook
            const experimentData = experimentDefinitions[experimentId];
            document.dispatchEvent(new CustomEvent('experimentStarted', {
                detail: experimentData
            }));

            // Update simulation type based on experiment
            if (experimentId.includes('ecg')) {
                currentSimulationType = 'ecg';
            } else if (experimentId.includes('eeg')) {
                currentSimulationType = 'eeg';
            } else if (experimentId.includes('blood_pressure')) {
                currentSimulationType = 'bp';
            }

            // Start the simulation
            simulationRunning = true;
            updateWaveform();

            // Play start sound
            audioManager.play('measurement_complete');

            // Show success notification
            labNotebook.showNotification(`Started: ${experimentData.title}`, 'success');
        });

        document.getElementById('pauseSimulation').addEventListener('click', () => {
            simulationRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        });

        document.getElementById('resetSimulation').addEventListener('click', () => {
            simulationRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            waveformChart.data.datasets[0].data = [];
            waveformChart.update();
            
            // Reset electrode placements
            placedElectrodes = [];
            document.querySelectorAll('.placement-zone').forEach(zone => {
                zone.dataset.correct = 'false';
                zone.classList.remove('bg-secondary-400/20', 'border-secondary-400');
            });
            updatePlacementAccuracy();
        });

        // Parameter control listeners
        document.getElementById('heartRate').addEventListener('input', (e) => {
            document.getElementById('heartRateValue').textContent = e.target.value;
        });

        document.getElementById('amplitude').addEventListener('input', (e) => {
            document.getElementById('amplitudeValue').textContent = e.target.value;
            document.getElementById('amplitudeDisplay').textContent = `${e.target.value} mV`;
        });

        document.getElementById('noiseLevel').addEventListener('input', (e) => {
            document.getElementById('noiseLevelValue').textContent = e.target.value;
        });

        // Artifact injection control listeners
        document.getElementById('baselineWander').addEventListener('input', (e) => {
            document.getElementById('baselineWanderValue').textContent = e.target.value;
            updateSignalQuality();

            // Log artifact injection
            if (parseFloat(e.target.value) > 0) {
                document.dispatchEvent(new CustomEvent('resultObtained', {
                    detail: {
                        resultType: 'artifact_injection',
                        data: { type: 'baseline_wander', level: e.target.value },
                        description: `Baseline wander artifact injected: ${e.target.value}`
                    }
                }));
            }
        });

        document.getElementById('emgArtifact').addEventListener('input', (e) => {
            document.getElementById('emgArtifactValue').textContent = e.target.value;
            updateSignalQuality();

            if (parseFloat(e.target.value) > 0) {
                document.dispatchEvent(new CustomEvent('resultObtained', {
                    detail: {
                        resultType: 'artifact_injection',
                        data: { type: 'emg_artifact', level: e.target.value },
                        description: `EMG artifact injected: ${e.target.value}`
                    }
                }));
            }
        });

        document.getElementById('powerlineInterference').addEventListener('input', (e) => {
            document.getElementById('powerlineInterferenceValue').textContent = e.target.value;
            updateSignalQuality();

            if (parseFloat(e.target.value) > 0) {
                document.dispatchEvent(new CustomEvent('resultObtained', {
                    detail: {
                        resultType: 'artifact_injection',
                        data: { type: 'powerline_interference', level: e.target.value },
                        description: `50Hz interference injected: ${e.target.value}`
                    }
                }));
            }
        });

        document.getElementById('injectRandomArtifacts').addEventListener('click', () => {
            // Inject random levels of artifacts
            const baselineLevel = Math.random() * 0.5;
            const emgLevel = Math.random() * 0.3;
            const powerlineLevel = Math.random() * 0.4;

            document.getElementById('baselineWander').value = baselineLevel.toFixed(1);
            document.getElementById('emgArtifact').value = emgLevel.toFixed(1);
            document.getElementById('powerlineInterference').value = powerlineLevel.toFixed(1);

            // Update displays
            document.getElementById('baselineWanderValue').textContent = baselineLevel.toFixed(1);
            document.getElementById('emgArtifactValue').textContent = emgLevel.toFixed(1);
            document.getElementById('powerlineInterferenceValue').textContent = powerlineLevel.toFixed(1);

            updateSignalQuality();
            audioManager.play('step_complete');

            document.dispatchEvent(new CustomEvent('resultObtained', {
                detail: {
                    resultType: 'artifact_injection',
                    data: { type: 'random_artifacts', baseline: baselineLevel, emg: emgLevel, powerline: powerlineLevel },
                    description: `Random artifacts injected - Baseline: ${baselineLevel.toFixed(1)}, EMG: ${emgLevel.toFixed(1)}, 50Hz: ${powerlineLevel.toFixed(1)}`
                }
            }));
        });

        document.getElementById('clearAllArtifacts').addEventListener('click', () => {
            document.getElementById('baselineWander').value = 0;
            document.getElementById('emgArtifact').value = 0;
            document.getElementById('powerlineInterference').value = 0;

            document.getElementById('baselineWanderValue').textContent = '0';
            document.getElementById('emgArtifactValue').textContent = '0';
            document.getElementById('powerlineInterferenceValue').textContent = '0';

            updateSignalQuality();
            audioManager.play('step_complete');

            document.dispatchEvent(new CustomEvent('resultObtained', {
                detail: {
                    resultType: 'artifact_removal',
                    data: { action: 'clear_all' },
                    description: 'All artifacts cleared - signal cleaned'
                }
            }));
        });

        document.getElementById('simulationType').addEventListener('change', (e) => {
            currentSimulationType = e.target.value;
            
            // Update chart color based on simulation type
            let color = '#60a5fa'; // primary
            if (currentSimulationType === 'eeg') color = '#34d399'; // secondary
            if (currentSimulationType === 'bp') color = '#ef4444'; // error
            
            waveformChart.data.datasets[0].borderColor = color;
            waveformChart.data.datasets[0].backgroundColor = color + '20';
            waveformChart.update();
        });

        // Drag and drop functionality
        let draggedElement = null;

        document.querySelectorAll('.electrode-component').forEach(component => {
            component.addEventListener('dragstart', (e) => {
                draggedElement = e.target;
                e.target.style.opacity = '0.5';
            });

            component.addEventListener('dragend', (e) => {
                e.target.style.opacity = '1';
                draggedElement = null;
            });
        });

        document.querySelectorAll('.placement-zone').forEach(zone => {
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.classList.add('bg-primary-400/20');
            });

            zone.addEventListener('dragleave', (e) => {
                zone.classList.remove('bg-primary-400/20');
            });

            zone.addEventListener('drop', (e) => {
                e.preventDefault();
                zone.classList.remove('bg-primary-400/20');
                
                if (draggedElement) {
                    const electrodeType = draggedElement.dataset.type;
                    const electrodeName = draggedElement.dataset.name;
                    const zoneId = zone.dataset.zone;
                    
                    // Check if placement is correct
                    let isCorrect = false;
                    if (electrodeType === 'ecg-lead' && ['ra', 'la', 'll'].includes(zoneId)) {
                        isCorrect = true;
                    } else if (electrodeType === 'eeg-electrode' && ['f3', 'f4'].includes(zoneId)) {
                        isCorrect = true;
                    } else if (electrodeType === 'bp-cuff' && zoneId === 'arm') {
                        isCorrect = true;
                    }
                    
                    if (isCorrect) {
                        zone.dataset.correct = 'true';
                        zone.classList.add('bg-secondary-400/20', 'border-secondary-400');
                        placedElectrodes.push({zone: zoneId, electrode: electrodeName});

                        // Show success feedback
                        showPlacementFeedback(`✓ ${electrodeName} correctly placed on ${zoneId.toUpperCase()}`, 'success');

                        // Dispatch procedure step completion event
                        const stepId = `place_${zoneId.toLowerCase()}_electrode`;
                        document.dispatchEvent(new CustomEvent('procedureStepCompleted', {
                            detail: { stepId: stepId }
                        }));

                        // Log result
                        document.dispatchEvent(new CustomEvent('resultObtained', {
                            detail: {
                                resultType: 'electrode_placement',
                                data: {
                                    position: zoneId.toUpperCase(),
                                    electrode: electrodeName,
                                    type: electrodeType
                                },
                                description: `Electrode placed correctly: ${electrodeName} on ${zoneId.toUpperCase()}`
                            }
                        }));

                        // Play success sound
                        audioManager.play('electrode_snap');

                    } else {
                        showPlacementFeedback(`✗ Incorrect placement. Try again.`, 'error');
                        labNotebook.showNotification(`Incorrect electrode placement for ${zoneId.toUpperCase()}`, 'warning');
                    }

                    updatePlacementAccuracy();
                }
            });
        });

        // Update placement accuracy
        function updatePlacementAccuracy() {
            const totalZones = document.querySelectorAll('.placement-zone').length;
            const correctPlacements = document.querySelectorAll('.placement-zone[data-correct="true"]').length;
            const accuracy = Math.round((correctPlacements / totalZones) * 100);
            document.getElementById('placementAccuracy').textContent = `${accuracy}%`;
        }

        // Show placement feedback
        function showPlacementFeedback(message, type) {
            const feedback = document.getElementById('placementFeedback');
            const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
            const color = type === 'success' ? 'text-secondary' : 'text-error-500';
            
            feedback.innerHTML = `
                <div class="flex items-center space-x-3">
                    <i class="fas ${icon} ${color}"></i>
                    <p class="text-sm text-text-secondary">${message}</p>
                </div>
            `;
            
            setTimeout(() => {
                feedback.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-info-circle text-accent-400"></i>
                        <p class="text-sm text-text-secondary">Drag electrodes from the component palette to the correct anatomical positions on the body diagram.</p>
                    </div>
                `;
            }, 3000);
        }

        // Help modal functionality
        document.getElementById('helpButton').addEventListener('click', () => {
            document.getElementById('helpModal').classList.remove('hidden');
        });

        document.getElementById('closeHelp').addEventListener('click', () => {
            document.getElementById('helpModal').classList.add('hidden');
        });

        document.getElementById('helpModal').addEventListener('click', (e) => {
            if (e.target === document.getElementById('helpModal')) {
                document.getElementById('helpModal').classList.add('hidden');
            }
        });

        // Save progress functionality
        document.getElementById('saveProgress').addEventListener('click', () => {
            const progress = {
                simulationType: currentSimulationType,
                placedElectrodes: placedElectrodes,
                parameters: {
                    heartRate: document.getElementById('heartRate').value,
                    amplitude: document.getElementById('amplitude').value,
                    noiseLevel: document.getElementById('noiseLevel').value
                },
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem('labProgress', JSON.stringify(progress));
            
            // Show success message
            const button = document.getElementById('saveProgress');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check mr-2"></i>Saved!';
            button.classList.add('bg-secondary', 'hover:bg-secondary-600');
            button.classList.remove('btn-secondary');
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-secondary', 'hover:bg-secondary-600');
                button.classList.add('btn-secondary');
            }, 2000);
        });

        // Helper functions for experiment management
        function getCurrentLeadConfiguration() {
            const raPlaced = placedElectrodes.some(e => e.zone === 'ra');
            const laPlaced = placedElectrodes.some(e => e.zone === 'la');
            const llPlaced = placedElectrodes.some(e => e.zone === 'll');

            if (raPlaced && laPlaced && llPlaced) return 'Lead II (RA-LL)';
            else if (raPlaced && llPlaced) return 'Lead II';
            else if (laPlaced && llPlaced) return 'Lead III';
            else if (raPlaced && laPlaced) return 'Lead I';
            return 'Incomplete';
        }

        function checkExperimentProgress() {
            if (!labNotebook.currentExperiment) return;

            const procedure = labNotebook.currentExperiment.procedure;
            const completedSteps = Object.values(procedure).filter(step => step.completed).length;
            const totalSteps = Object.keys(procedure).length;

            if (completedSteps === totalSteps) {
                labNotebook.showNotification('Experiment completed! You can now export your report.', 'success');
                document.getElementById('export-pdf-btn').disabled = false;
                audioManager.play('measurement_complete');
            }
        }

        // Signal quality assessment
        function updateSignalQuality() {
            const baseline = parseFloat(document.getElementById('baselineWander').value);
            const emg = parseFloat(document.getElementById('emgArtifact').value);
            const powerline = parseFloat(document.getElementById('powerlineInterference').value);
            const noise = parseFloat(document.getElementById('noiseLevel').value);

            // Calculate overall artifact level
            const totalArtifacts = baseline + emg + powerline + noise;

            let quality, qualityClass, snr;

            if (totalArtifacts < 0.2) {
                quality = 'Excellent';
                qualityClass = 'signal-quality-excellent';
                snr = 40 + Math.random() * 10; // 40-50 dB
            } else if (totalArtifacts < 0.5) {
                quality = 'Good';
                qualityClass = 'signal-quality-good';
                snr = 25 + Math.random() * 15; // 25-40 dB
            } else if (totalArtifacts < 0.8) {
                quality = 'Poor';
                qualityClass = 'signal-quality-poor';
                snr = 10 + Math.random() * 15; // 10-25 dB
            } else {
                quality = 'Critical';
                qualityClass = 'signal-quality-critical';
                snr = Math.random() * 10; // 0-10 dB

                // Play alarm for critical signal quality
                if (totalArtifacts > 1.5) {
                    audioManager.play('alarm_tachy');
                }
            }

            // Update UI
            const qualityElement = document.getElementById('signalQuality');
            qualityElement.textContent = quality;
            qualityElement.className = `font-medium ${qualityClass}`;

            // Log signal quality change
            document.dispatchEvent(new CustomEvent('resultObtained', {
                detail: {
                    resultType: 'signal_quality',
                    data: { quality, snr: snr.toFixed(1), artifacts: totalArtifacts.toFixed(2) },
                    description: `Signal Quality: ${quality} (SNR: ${snr.toFixed(1)} dB)`
                }
            }));

            return { quality, snr, totalArtifacts };
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            initializeChart();
            
            // Load saved progress if available
            const savedProgress = localStorage.getItem('labProgress');
            if (savedProgress) {
                const progress = JSON.parse(savedProgress);
                document.getElementById('simulationType').value = progress.simulationType;
                currentSimulationType = progress.simulationType;
                
                if (progress.parameters) {
                    document.getElementById('heartRate').value = progress.parameters.heartRate;
                    document.getElementById('amplitude').value = progress.parameters.amplitude;
                    document.getElementById('noiseLevel').value = progress.parameters.noiseLevel;
                    
                    // Update display values
                    document.getElementById('heartRateValue').textContent = progress.parameters.heartRate;
                    document.getElementById('amplitudeValue').textContent = progress.parameters.amplitude;
                    document.getElementById('noiseLevelValue').textContent = progress.parameters.noiseLevel;
                }
            }
        });
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
</body>
</html>