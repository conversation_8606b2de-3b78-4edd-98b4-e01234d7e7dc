@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;500&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary Colors - Medical Authority */
  --color-primary: #1e3a8a; /* blue-800 */
  --color-primary-50: #eff6ff; /* blue-50 */
  --color-primary-100: #dbeafe; /* blue-100 */
  --color-primary-200: #bfdbfe; /* blue-200 */
  --color-primary-300: #93c5fd; /* blue-300 */
  --color-primary-400: #60a5fa; /* blue-400 */
  --color-primary-500: #3b82f6; /* blue-500 */
  --color-primary-600: #2563eb; /* blue-600 */
  --color-primary-700: #1d4ed8; /* blue-700 */
  --color-primary-800: #1e3a8a; /* blue-800 */
  --color-primary-900: #1e40af; /* blue-900 */

  /* Secondary Colors - Success & Learning Feedback */
  --color-secondary: #10b981; /* emerald-500 */
  --color-secondary-50: #ecfdf5; /* emerald-50 */
  --color-secondary-100: #d1fae5; /* emerald-100 */
  --color-secondary-200: #a7f3d0; /* emerald-200 */
  --color-secondary-300: #6ee7b7; /* emerald-300 */
  --color-secondary-400: #34d399; /* emerald-400 */
  --color-secondary-500: #10b981; /* emerald-500 */
  --color-secondary-600: #059669; /* emerald-600 */
  --color-secondary-700: #047857; /* emerald-700 */
  --color-secondary-800: #065f46; /* emerald-800 */
  --color-secondary-900: #064e3b; /* emerald-900 */

  /* Accent Colors - Interactive Elements & Achievements */
  --color-accent: #f59e0b; /* amber-500 */
  --color-accent-50: #fffbeb; /* amber-50 */
  --color-accent-100: #fef3c7; /* amber-100 */
  --color-accent-200: #fde68a; /* amber-200 */
  --color-accent-300: #fcd34d; /* amber-300 */
  --color-accent-400: #fbbf24; /* amber-400 */
  --color-accent-500: #f59e0b; /* amber-500 */
  --color-accent-600: #d97706; /* amber-600 */
  --color-accent-700: #b45309; /* amber-700 */
  --color-accent-800: #92400e; /* amber-800 */
  --color-accent-900: #78350f; /* amber-900 */

  /* Background Colors - Dark Theme Foundation */
  --color-background: #0f172a; /* slate-900 */
  --color-surface: #1e293b; /* slate-800 */
  --color-surface-light: #334155; /* slate-700 */

  /* Text Colors */
  --color-text-primary: #f8fafc; /* slate-50 */
  --color-text-secondary: #94a3b8; /* slate-400 */
  --color-text-muted: #64748b; /* slate-500 */

  /* Status Colors */
  --color-success: #22c55e; /* green-500 */
  --color-success-50: #f0fdf4; /* green-50 */
  --color-success-100: #dcfce7; /* green-100 */
  --color-success-500: #22c55e; /* green-500 */
  --color-success-600: #16a34a; /* green-600 */

  --color-warning: #eab308; /* yellow-500 */
  --color-warning-50: #fefce8; /* yellow-50 */
  --color-warning-100: #fef9c3; /* yellow-100 */
  --color-warning-500: #eab308; /* yellow-500 */
  --color-warning-600: #ca8a04; /* yellow-600 */

  --color-error: #ef4444; /* red-500 */
  --color-error-50: #fef2f2; /* red-50 */
  --color-error-100: #fee2e2; /* red-100 */
  --color-error-500: #ef4444; /* red-500 */
  --color-error-600: #dc2626; /* red-600 */

  /* Shadows */
  --shadow-subtle: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-moderate: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-dramatic: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Animation Durations */
  --duration-fast: 300ms;
  --duration-moderate: 400ms;
  --duration-slow: 600ms;
}

/* Custom Component Classes */
@layer components {
  .btn-primary {
    @apply bg-primary hover:bg-primary-700 text-white font-inter font-semibold px-6 py-3 rounded-lg transition-all duration-300 shadow-subtle hover:shadow-moderate;
  }

  .btn-secondary {
    @apply bg-secondary hover:bg-secondary-600 text-white font-inter font-semibold px-6 py-3 rounded-lg transition-all duration-300 shadow-subtle hover:shadow-moderate;
  }

  .btn-accent {
    @apply bg-accent hover:bg-accent-600 text-white font-inter font-semibold px-6 py-3 rounded-lg transition-all duration-300 shadow-subtle hover:shadow-moderate;
  }

  .card {
    @apply bg-surface rounded-xl p-6 shadow-subtle border border-slate-700;
  }

  .input-field {
    @apply bg-surface border border-slate-600 rounded-lg px-4 py-3 text-text-primary placeholder-text-secondary focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-300;
  }

  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent;
  }

  .text-gradient-accent {
    @apply bg-gradient-to-r from-accent-400 to-accent-600 bg-clip-text text-transparent;
  }
}

/* Custom Animations */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn var(--duration-fast) ease-out;
  }

  .animate-slide-up {
    animation: slideUp var(--duration-moderate) ease-out;
  }

  .animate-celebration {
    animation: celebration var(--duration-slow) ease-out;
  }

  .transition-smooth {
    transition: all var(--duration-fast) ease-out;
  }

  .transition-physics {
    transition: all var(--duration-moderate) cubic-bezier(0.34, 1.56, 0.64, 1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes celebration {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Scrollbar Styling for Dark Theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-background);
}

::-webkit-scrollbar-thumb {
  background: var(--color-surface-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}