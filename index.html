<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>BioMed Lab Pro</title>
    <link rel="stylesheet" href="css/main.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
        }
        
        .loading-container {
            text-align: center;
            color: white;
        }
        
        .logo {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .loading-text {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .fade-out {
            opacity: 0;
            transition: opacity 0.5s ease-out;
        }
    </style>
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fbiomedlab3601back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.6"></script>
</head>
<body>
    <div class="loading-container" id="loadingContainer">
        <div class="logo">🧬 BioMed Lab Pro</div>
        <div class="loading-text">Initializing Laboratory Environment...</div>
        <div class="spinner"></div>
    </div>

    <script>
        // Simulate loading time and redirect to dashboard
        setTimeout(function() {
            document.getElementById('loadingContainer').classList.add('fade-out');
            
            setTimeout(function() {
                window.location.href = 'pages/dashboard_hub.html';
            }, 500);
        }, 2000);
        
        // Fallback redirect in case of any issues
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (window.location.pathname.endsWith('index.html') || window.location.pathname.endsWith('/')) {
                    window.location.href = 'pages/dashboard_hub.html';
                }
            }, 3000);
        });
    </script>
<script id="dhws-dataInjector" src="/public/dhws-data-injector.js"></script>
</body>
</html>