<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Module Explorer - BioMed Lab Pro</title>
    <link rel="stylesheet" href="../css/main.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fbiomedlab3601back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.6"></script>
</head>
<body class="bg-background text-text-primary font-inter min-h-screen">
    <!-- Header Command Center -->
    <header class="bg-surface border-b border-slate-700 sticky top-0 z-50 backdrop-blur-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo and Brand -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-heartbeat text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gradient-primary">BioMed Lab Pro</h1>
                            <p class="text-xs text-text-secondary">Educational Technology Platform</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="dashboard_hub.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                    </a>
                    <a href="module_explorer.html" class="text-primary-400 font-semibold border-b-2 border-primary-400 pb-1">
                        <i class="fas fa-compass mr-2"></i>Explore
                    </a>
                    <a href="simulation_laboratory.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-flask mr-2"></i>Laboratory
                    </a>
                    <a href="circuit_design_workbench.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-microchip mr-2"></i>Workbench
                    </a>
                </nav>

                <!-- Bilingual Toggle and User Actions -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center bg-surface-light rounded-lg p-1">
                        <button id="langEn" class="px-3 py-1 rounded-md text-sm font-medium bg-primary text-white transition-all duration-300">
                            EN
                        </button>
                        <button id="langAr" class="px-3 py-1 rounded-md text-sm font-medium text-text-secondary hover:text-white transition-all duration-300">
                            عربي
                        </button>
                    </div>
                    <div class="w-8 h-8 bg-gradient-to-br from-accent-400 to-accent-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <button class="md:hidden text-text-secondary hover:text-white">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Module Explorer Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Page Header with Search and Filters -->
        <section class="mb-8">
            <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
                <div>
                    <h2 class="text-3xl font-bold text-gradient-primary mb-2">Module Explorer</h2>
                    <p class="text-text-secondary text-lg">Discover biomedical engineering modules tailored to your learning journey</p>
                </div>
                
                <!-- Search and Filter Controls -->
                <div class="flex flex-col sm:flex-row gap-4 w-full lg:w-auto">
                    <!-- Search Bar -->
                    <div class="relative">
                        <input type="text" id="moduleSearch" placeholder="Search modules..." class="input-field pl-10 pr-4 w-full sm:w-64" />
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary"></i>
                    </div>
                    
                    <!-- Filter Button -->
                    <button id="filterToggle" class="btn-accent flex items-center space-x-2">
                        <i class="fas fa-filter"></i>
                        <span>Filters</span>
                    </button>
                </div>
            </div>

            <!-- Filter Panel -->
            <div id="filterPanel" class="hidden mt-6 p-6 bg-surface rounded-xl border border-slate-700">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Difficulty Filter -->
                    <div>
                        <h3 class="text-sm font-semibold text-white mb-3">Difficulty Level</h3>
                        <div class="space-y-2">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="difficulty-filter" value="beginner" checked />
                                <span class="w-3 h-3 bg-secondary rounded-full"></span>
                                <span class="text-sm text-text-secondary">Beginner</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="difficulty-filter" value="intermediate" checked />
                                <span class="w-3 h-3 bg-primary rounded-full"></span>
                                <span class="text-sm text-text-secondary">Intermediate</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="difficulty-filter" value="advanced" checked />
                                <span class="w-3 h-3 bg-accent rounded-full"></span>
                                <span class="text-sm text-text-secondary">Advanced</span>
                            </label>
                        </div>
                    </div>

                    <!-- Topic Area Filter -->
                    <div>
                        <h3 class="text-sm font-semibold text-white mb-3">Topic Area</h3>
                        <div class="space-y-2">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="topic-filter" value="cardiovascular" checked />
                                <span class="text-sm text-text-secondary">Cardiovascular</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="topic-filter" value="neurological" checked />
                                <span class="text-sm text-text-secondary">Neurological</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="topic-filter" value="circuit-design" checked />
                                <span class="text-sm text-text-secondary">Circuit Design</span>
                            </label>
                        </div>
                    </div>

                    <!-- Completion Status Filter -->
                    <div>
                        <h3 class="text-sm font-semibold text-white mb-3">Completion Status</h3>
                        <div class="space-y-2">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="status-filter" value="completed" checked />
                                <span class="text-sm text-text-secondary">Completed</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="status-filter" value="in-progress" checked />
                                <span class="text-sm text-text-secondary">In Progress</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="status-filter" value="not-started" checked />
                                <span class="text-sm text-text-secondary">Not Started</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Reset Filters -->
                <div class="mt-6 flex justify-end">
                    <button id="resetFilters" class="text-accent-400 hover:text-accent-300 text-sm font-medium">
                        <i class="fas fa-undo mr-1"></i>Reset Filters
                    </button>
                </div>
            </div>
        </section>

        <!-- Module Statistics -->
        <section class="mb-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="card text-center">
                    <div class="text-2xl font-bold text-secondary">24</div>
                    <div class="text-sm text-text-secondary">Total Modules</div>
                </div>
                <div class="card text-center">
                    <div class="text-2xl font-bold text-primary-400">8</div>
                    <div class="text-sm text-text-secondary">Completed</div>
                </div>
                <div class="card text-center">
                    <div class="text-2xl font-bold text-accent-400">3</div>
                    <div class="text-sm text-text-secondary">In Progress</div>
                </div>
                <div class="card text-center">
                    <div class="text-2xl font-bold text-text-secondary">13</div>
                    <div class="text-sm text-text-secondary">Available</div>
                </div>
            </div>
        </section>

        <!-- Module Grid -->
        <section id="moduleGrid">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <!-- ECG Fundamentals - Completed -->
                <div class="module-card card hover:shadow-glow-secondary transition-all duration-300 cursor-pointer group" data-difficulty="beginner" data-topic="cardiovascular" data-status="completed">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="ECG Waveform" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute top-2 left-2">
                            <span class="bg-secondary text-white text-xs px-2 py-1 rounded-full">Beginner</span>
                        </div>
                        <div class="absolute top-2 right-2">
                            <div class="w-6 h-6 bg-secondary rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                        </div>
                        <div class="absolute bottom-2 left-2">
                            <i class="fas fa-heartbeat text-secondary text-xl"></i>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <h3 class="font-semibold text-white group-hover:text-secondary transition-colors">ECG Fundamentals</h3>
                        <p class="text-sm text-text-secondary">Master electrocardiography basics and signal analysis techniques</p>
                        <div class="flex items-center justify-between text-xs text-text-secondary">
                            <span><i class="fas fa-clock mr-1"></i>2.5 hours</span>
                            <span><i class="fas fa-star mr-1"></i>4.8/5</span>
                        </div>
                        <div class="w-full bg-surface-light rounded-full h-2">
                            <div class="bg-secondary h-2 rounded-full" style="width: 100%"></div>
                        </div>
                    </div>
                </div>

                <!-- EEG Signal Processing - In Progress -->
                <div class="module-card card hover:shadow-glow-primary transition-all duration-300 cursor-pointer group" data-difficulty="intermediate" data-topic="neurological" data-status="in-progress">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pexels.com/photos/8376277/pexels-photo-8376277.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="EEG Brainwaves" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute top-2 left-2">
                            <span class="bg-primary text-white text-xs px-2 py-1 rounded-full">Intermediate</span>
                        </div>
                        <div class="absolute top-2 right-2">
                            <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center animate-pulse">
                                <i class="fas fa-play text-white text-xs"></i>
                            </div>
                        </div>
                        <div class="absolute bottom-2 left-2">
                            <i class="fas fa-brain text-primary-400 text-xl"></i>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <h3 class="font-semibold text-white group-hover:text-primary-400 transition-colors">EEG Signal Processing</h3>
                        <p class="text-sm text-text-secondary">Advanced electroencephalography and brain signal analysis</p>
                        <div class="flex items-center justify-between text-xs text-text-secondary">
                            <span><i class="fas fa-clock mr-1"></i>4.0 hours</span>
                            <span><i class="fas fa-star mr-1"></i>4.9/5</span>
                        </div>
                        <div class="w-full bg-surface-light rounded-full h-2">
                            <div class="bg-primary h-2 rounded-full" style="width: 65%"></div>
                        </div>
                    </div>
                </div>

                <!-- Biomedical Amplifier Design - Advanced -->
                <div class="module-card card hover:shadow-glow-accent transition-all duration-300 cursor-pointer group" data-difficulty="advanced" data-topic="circuit-design" data-status="not-started">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pixabay.com/photo/2017/08/10/08/47/laptop-2619463_1280.jpg" alt="Circuit Design" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute top-2 left-2">
                            <span class="bg-accent text-white text-xs px-2 py-1 rounded-full">Advanced</span>
                        </div>
                        <div class="absolute top-2 right-2">
                            <div class="w-6 h-6 bg-surface-light rounded-full flex items-center justify-center">
                                <i class="fas fa-lock text-text-secondary text-xs"></i>
                            </div>
                        </div>
                        <div class="absolute bottom-2 left-2">
                            <i class="fas fa-microchip text-accent-400 text-xl"></i>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <h3 class="font-semibold text-white group-hover:text-accent-400 transition-colors">Biomedical Amplifier Design</h3>
                        <p class="text-sm text-text-secondary">Design and build high-precision medical amplifier circuits</p>
                        <div class="flex items-center justify-between text-xs text-text-secondary">
                            <span><i class="fas fa-clock mr-1"></i>6.5 hours</span>
                            <span><i class="fas fa-star mr-1"></i>4.7/5</span>
                        </div>
                        <div class="w-full bg-surface-light rounded-full h-2">
                            <div class="bg-accent h-2 rounded-full" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Blood Pressure Monitoring -->
                <div class="module-card card hover:shadow-glow-secondary transition-all duration-300 cursor-pointer group" data-difficulty="beginner" data-topic="cardiovascular" data-status="not-started">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pexels.com/photos/4386466/pexels-photo-4386466.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Blood Pressure Monitor" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute top-2 left-2">
                            <span class="bg-secondary text-white text-xs px-2 py-1 rounded-full">Beginner</span>
                        </div>
                        <div class="absolute bottom-2 left-2">
                            <i class="fas fa-heartbeat text-error-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <h3 class="font-semibold text-white group-hover:text-secondary transition-colors">Blood Pressure Monitoring</h3>
                        <p class="text-sm text-text-secondary">Non-invasive BP measurement techniques and analysis</p>
                        <div class="flex items-center justify-between text-xs text-text-secondary">
                            <span><i class="fas fa-clock mr-1"></i>3.0 hours</span>
                            <span><i class="fas fa-star mr-1"></i>4.6/5</span>
                        </div>
                        <div class="w-full bg-surface-light rounded-full h-2">
                            <div class="bg-secondary h-2 rounded-full" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- EMG Signal Analysis -->
                <div class="module-card card hover:shadow-glow-primary transition-all duration-300 cursor-pointer group" data-difficulty="intermediate" data-topic="neurological" data-status="not-started">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="EMG Signals" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute top-2 left-2">
                            <span class="bg-primary text-white text-xs px-2 py-1 rounded-full">Intermediate</span>
                        </div>
                        <div class="absolute bottom-2 left-2">
                            <i class="fas fa-wave-square text-primary-400 text-xl"></i>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <h3 class="font-semibold text-white group-hover:text-primary-400 transition-colors">EMG Signal Analysis</h3>
                        <p class="text-sm text-text-secondary">Electromyography and muscle signal processing techniques</p>
                        <div class="flex items-center justify-between text-xs text-text-secondary">
                            <span><i class="fas fa-clock mr-1"></i>3.5 hours</span>
                            <span><i class="fas fa-star mr-1"></i>4.5/5</span>
                        </div>
                        <div class="w-full bg-surface-light rounded-full h-2">
                            <div class="bg-primary h-2 rounded-full" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Filter Circuit Design -->
                <div class="module-card card hover:shadow-glow-accent transition-all duration-300 cursor-pointer group" data-difficulty="advanced" data-topic="circuit-design" data-status="not-started">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pixabay.com/photo/2018/05/08/08/44/artificial-intelligence-3382507_1280.jpg" alt="Filter Circuits" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute top-2 left-2">
                            <span class="bg-accent text-white text-xs px-2 py-1 rounded-full">Advanced</span>
                        </div>
                        <div class="absolute bottom-2 left-2">
                            <i class="fas fa-filter text-accent-400 text-xl"></i>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <h3 class="font-semibold text-white group-hover:text-accent-400 transition-colors">Filter Circuit Design</h3>
                        <p class="text-sm text-text-secondary">Design active and passive filters for biomedical applications</p>
                        <div class="flex items-center justify-between text-xs text-text-secondary">
                            <span><i class="fas fa-clock mr-1"></i>5.0 hours</span>
                            <span><i class="fas fa-star mr-1"></i>4.8/5</span>
                        </div>
                        <div class="w-full bg-surface-light rounded-full h-2">
                            <div class="bg-accent h-2 rounded-full" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Pulse Oximetry -->
                <div class="module-card card hover:shadow-glow-secondary transition-all duration-300 cursor-pointer group" data-difficulty="beginner" data-topic="cardiovascular" data-status="completed">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pexels.com/photos/4386467/pexels-photo-4386467.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Pulse Oximeter" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute top-2 left-2">
                            <span class="bg-secondary text-white text-xs px-2 py-1 rounded-full">Beginner</span>
                        </div>
                        <div class="absolute top-2 right-2">
                            <div class="w-6 h-6 bg-secondary rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                        </div>
                        <div class="absolute bottom-2 left-2">
                            <i class="fas fa-lungs text-secondary text-xl"></i>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <h3 class="font-semibold text-white group-hover:text-secondary transition-colors">Pulse Oximetry</h3>
                        <p class="text-sm text-text-secondary">Optical measurement of blood oxygen saturation levels</p>
                        <div class="flex items-center justify-between text-xs text-text-secondary">
                            <span><i class="fas fa-clock mr-1"></i>2.0 hours</span>
                            <span><i class="fas fa-star mr-1"></i>4.4/5</span>
                        </div>
                        <div class="w-full bg-surface-light rounded-full h-2">
                            <div class="bg-secondary h-2 rounded-full" style="width: 100%"></div>
                        </div>
                    </div>
                </div>

                <!-- Additional modules would continue here... -->
            </div>

            <!-- No Results Message -->
            <div id="noResults" class="hidden text-center py-12">
                <div class="w-16 h-16 bg-surface-light rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-search text-text-secondary text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">No modules found</h3>
                <p class="text-text-secondary">Try adjusting your search criteria or filters</p>
            </div>
        </section>

        <!-- Learning Path Recommendations -->
        <section class="mt-12">
            <h3 class="text-xl font-semibold text-white mb-6">Recommended Learning Paths</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Cardiovascular Specialist Path -->
                <div class="card hover:shadow-glow-secondary transition-all duration-300">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-12 h-12 bg-secondary rounded-lg flex items-center justify-center">
                            <i class="fas fa-heartbeat text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-white">Cardiovascular Specialist</h4>
                            <p class="text-sm text-text-secondary">6 modules • 18 hours</p>
                        </div>
                    </div>
                    <p class="text-sm text-text-secondary mb-4">Master ECG, blood pressure, and cardiac monitoring systems</p>
                    <div class="flex items-center justify-between">
                        <span class="text-xs text-secondary font-medium">33% Complete</span>
                        <button class="btn-secondary text-sm px-4 py-2">Continue Path</button>
                    </div>
                </div>

                <!-- Neurological Engineer Path -->
                <div class="card hover:shadow-glow-primary transition-all duration-300">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-brain text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-white">Neurological Engineer</h4>
                            <p class="text-sm text-text-secondary">5 modules • 22 hours</p>
                        </div>
                    </div>
                    <p class="text-sm text-text-secondary mb-4">Specialize in EEG, EMG, and neural signal processing</p>
                    <div class="flex items-center justify-between">
                        <span class="text-xs text-primary-400 font-medium">20% Complete</span>
                        <button class="btn-primary text-sm px-4 py-2">Start Path</button>
                    </div>
                </div>

                <!-- Circuit Design Master Path -->
                <div class="card hover:shadow-glow-accent transition-all duration-300">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-12 h-12 bg-accent rounded-lg flex items-center justify-center">
                            <i class="fas fa-microchip text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-white">Circuit Design Master</h4>
                            <p class="text-sm text-text-secondary">8 modules • 35 hours</p>
                        </div>
                    </div>
                    <p class="text-sm text-text-secondary mb-4">Advanced amplifier and filter design for medical devices</p>
                    <div class="flex items-center justify-between">
                        <span class="text-xs text-accent-400 font-medium">0% Complete</span>
                        <button class="btn-accent text-sm px-4 py-2">Start Path</button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Floating Action Button -->
    <div class="fixed bottom-6 right-6 z-50">
        <button class="w-14 h-14 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full shadow-dramatic hover:shadow-glow-primary transition-all duration-300 flex items-center justify-center group">
            <i class="fas fa-plus text-white text-lg group-hover:scale-110 transition-transform duration-300"></i>
        </button>
    </div>

    <!-- JavaScript for Interactivity -->
    <script>
        // Language Toggle Functionality
        const langEn = document.getElementById('langEn');
        const langAr = document.getElementById('langAr');
        const htmlElement = document.documentElement;

        langEn.addEventListener('click', () => {
            htmlElement.setAttribute('lang', 'en');
            htmlElement.setAttribute('dir', 'ltr');
            langEn.classList.add('bg-primary', 'text-white');
            langEn.classList.remove('text-text-secondary');
            langAr.classList.remove('bg-primary', 'text-white');
            langAr.classList.add('text-text-secondary');
        });

        langAr.addEventListener('click', () => {
            htmlElement.setAttribute('lang', 'ar');
            htmlElement.setAttribute('dir', 'rtl');
            langAr.classList.add('bg-primary', 'text-white');
            langAr.classList.remove('text-text-secondary');
            langEn.classList.remove('bg-primary', 'text-white');
            langEn.classList.add('text-text-secondary');
        });

        // Filter Panel Toggle
        const filterToggle = document.getElementById('filterToggle');
        const filterPanel = document.getElementById('filterPanel');

        filterToggle.addEventListener('click', () => {
            filterPanel.classList.toggle('hidden');
        });

        // Search Functionality
        const searchInput = document.getElementById('moduleSearch');
        const moduleCards = document.querySelectorAll('.module-card');
        const noResults = document.getElementById('noResults');

        function filterModules() {
            const searchTerm = searchInput.value.toLowerCase();
            const difficultyFilters = Array.from(document.querySelectorAll('.difficulty-filter:checked')).map(cb => cb.value);
            const topicFilters = Array.from(document.querySelectorAll('.topic-filter:checked')).map(cb => cb.value);
            const statusFilters = Array.from(document.querySelectorAll('.status-filter:checked')).map(cb => cb.value);

            let visibleCount = 0;

            moduleCards.forEach(card => {
                const title = card.querySelector('h3').textContent.toLowerCase();
                const description = card.querySelector('p').textContent.toLowerCase();
                const difficulty = card.dataset.difficulty;
                const topic = card.dataset.topic;
                const status = card.dataset.status;

                const matchesSearch = title.includes(searchTerm) || description.includes(searchTerm);
                const matchesDifficulty = difficultyFilters.includes(difficulty);
                const matchesTopic = topicFilters.includes(topic);
                const matchesStatus = statusFilters.includes(status);

                if (matchesSearch && matchesDifficulty && matchesTopic && matchesStatus) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            noResults.classList.toggle('hidden', visibleCount > 0);
        }

        // Event Listeners for Filtering
        searchInput.addEventListener('input', filterModules);
        document.querySelectorAll('.difficulty-filter, .topic-filter, .status-filter').forEach(filter => {
            filter.addEventListener('change', filterModules);
        });

        // Reset Filters
        document.getElementById('resetFilters').addEventListener('click', () => {
            searchInput.value = '';
            document.querySelectorAll('.difficulty-filter, .topic-filter, .status-filter').forEach(filter => {
                filter.checked = true;
            });
            filterModules();
        });

        // Module Card Click Handlers
        moduleCards.forEach(card => {
            card.addEventListener('click', () => {
                const title = card.querySelector('h3').textContent;
                const status = card.dataset.status;
                
                if (status === 'completed' || status === 'in-progress') {
                    window.location.href = 'simulation_laboratory.html';
                } else {
                    // Show module details or start module
                    console.log(`Starting module: ${title}`);
                }
            });
        });

        // Learning Path Buttons
        document.querySelectorAll('.card button').forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const pathName = button.closest('.card').querySelector('h4').textContent;
                console.log(`Starting learning path: ${pathName}`);
                window.location.href = 'simulation_laboratory.html';
            });
        });

        // Floating Action Button
        document.querySelector('.fixed button').addEventListener('click', () => {
            filterPanel.classList.remove('hidden');
            searchInput.focus();
        });

        // Animate cards on load
        window.addEventListener('load', () => {
            moduleCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.4s ease-out';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 50);
                }, index * 100);
            });
        });
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
</body>
</html>