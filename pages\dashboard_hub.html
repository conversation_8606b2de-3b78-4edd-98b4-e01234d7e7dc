<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard Hub - BioMed Lab Pro</title>
    <link rel="stylesheet" href="../css/main.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fbiomedlab3601back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.6"></script>
</head>
<body class="bg-background text-text-primary font-inter min-h-screen">
    <!-- Header Command Center -->
    <header class="bg-surface border-b border-slate-700 sticky top-0 z-50 backdrop-blur-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo and Brand -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-heartbeat text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gradient-primary">BioMed Lab Pro</h1>
                            <p class="text-xs text-text-secondary">Educational Technology Platform</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="dashboard_hub.html" class="text-primary-400 font-semibold border-b-2 border-primary-400 pb-1">
                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                    </a>
                    <a href="module_explorer.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-compass mr-2"></i>Explore
                    </a>
                    <a href="simulation_laboratory.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-flask mr-2"></i>Laboratory
                    </a>
                    <a href="circuit_design_workbench.html" class="text-text-secondary hover:text-primary-400 transition-colors duration-300">
                        <i class="fas fa-microchip mr-2"></i>Workbench
                    </a>
                </nav>

                <!-- Bilingual Toggle and User Actions -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center bg-surface-light rounded-lg p-1">
                        <button id="langEn" class="px-3 py-1 rounded-md text-sm font-medium bg-primary text-white transition-all duration-300">
                            EN
                        </button>
                        <button id="langAr" class="px-3 py-1 rounded-md text-sm font-medium text-text-secondary hover:text-white transition-all duration-300">
                            عربي
                        </button>
                    </div>
                    <div class="w-8 h-8 bg-gradient-to-br from-accent-400 to-accent-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <button class="md:hidden text-text-secondary hover:text-white">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Dashboard Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Welcome Section with Academic Credentials -->
        <section class="mb-8">
            <div class="bg-gradient-to-r from-primary-900/50 to-secondary-900/50 rounded-2xl p-8 border border-slate-700">
                <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between">
                    <div class="flex-1">
                        <h2 class="text-3xl font-bold text-gradient-primary mb-2">Welcome back, Student!</h2>
                        <p class="text-text-secondary text-lg mb-4">Continue your biomedical engineering journey</p>
                        <div class="bg-surface/50 rounded-lg p-4 border border-slate-600">
                            <div class="flex items-center space-x-3 mb-2">
                                <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Dr. Mohammed Yagoub Esmail" class="w-12 h-12 rounded-full object-cover border-2 border-primary-400" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                                <div>
                                    <h3 class="font-semibold text-white">Dr. Mohammed Yagoub Esmail</h3>
                                    <p class="text-sm text-text-secondary">Professor of Biomedical Engineering</p>
                                </div>
                            </div>
                            <p class="text-sm text-text-secondary">University of Ottawa • IEEE Senior Member • 15+ Years Experience</p>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 lg:ml-8">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-accent-400">78%</div>
                            <div class="text-sm text-text-secondary">Overall Progress</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Learning Path and Progress -->
        <section class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Visual Learning Path -->
            <div class="lg:col-span-2">
                <div class="card">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-white">Learning Path</h3>
                        <span class="text-sm text-text-secondary">Last updated: July 18, 2025</span>
                    </div>
                    
                    <div class="space-y-6">
                        <!-- Completed Module -->
                        <div class="flex items-center space-x-4 p-4 bg-secondary-900/20 rounded-lg border border-secondary-700">
                            <div class="relative">
                                <div class="w-12 h-12 bg-secondary rounded-full flex items-center justify-center">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                                <div class="absolute -top-1 -right-1 w-6 h-6 bg-accent rounded-full flex items-center justify-center">
                                    <i class="fas fa-star text-white text-xs"></i>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-white">ECG Fundamentals</h4>
                                <p class="text-sm text-text-secondary">Electrocardiography basics and signal analysis</p>
                                <div class="mt-2 flex items-center space-x-2">
                                    <div class="w-full bg-surface-light rounded-full h-2">
                                        <div class="bg-secondary h-2 rounded-full" style="width: 100%"></div>
                                    </div>
                                    <span class="text-xs text-secondary font-medium">100%</span>
                                </div>
                            </div>
                        </div>

                        <!-- In Progress Module -->
                        <div class="flex items-center space-x-4 p-4 bg-primary-900/20 rounded-lg border border-primary-700">
                            <div class="relative">
                                <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                                    <i class="fas fa-brain text-white"></i>
                                </div>
                                <div class="absolute -inset-1 border-2 border-primary-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-white">EEG Basics</h4>
                                <p class="text-sm text-text-secondary">Electroencephalography and brain signal processing</p>
                                <div class="mt-2 flex items-center space-x-2">
                                    <div class="w-full bg-surface-light rounded-full h-2">
                                        <div class="bg-primary h-2 rounded-full" style="width: 65%"></div>
                                    </div>
                                    <span class="text-xs text-primary-400 font-medium">65%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Next Recommended -->
                        <div class="flex items-center space-x-4 p-4 bg-surface-light/50 rounded-lg border border-slate-600">
                            <div class="w-12 h-12 bg-surface-light rounded-full flex items-center justify-center">
                                <i class="fas fa-heartbeat text-text-secondary"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-white">Blood Pressure Monitoring</h4>
                                <p class="text-sm text-text-secondary">Non-invasive BP measurement techniques</p>
                                <div class="mt-2">
                                    <span class="text-xs text-accent-400 font-medium">Recommended Next</span>
                                </div>
                            </div>
                            <button class="btn-accent text-sm px-4 py-2">
                                Start Learning
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar with Stats and Achievements -->
            <div class="space-y-6">
                <!-- Study Streak -->
                <div class="card">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-accent-400 to-accent-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-fire text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-accent-400">12</h3>
                        <p class="text-sm text-text-secondary">Day Study Streak</p>
                    </div>
                </div>

                <!-- Recent Achievements -->
                <div class="card">
                    <h3 class="text-lg font-semibold text-white mb-4">Recent Achievements</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 p-3 bg-secondary-900/20 rounded-lg">
                            <div class="w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                                <i class="fas fa-trophy text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-white">ECG Master</p>
                                <p class="text-xs text-text-secondary">Completed all ECG modules</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3 p-3 bg-primary-900/20 rounded-lg">
                            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                <i class="fas fa-medal text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-white">Circuit Designer</p>
                                <p class="text-xs text-text-secondary">Built first amplifier circuit</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Personalized Recommendations -->
                <div class="card">
                    <h3 class="text-lg font-semibold text-white mb-4">Recommended for You</h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-surface-light/50 rounded-lg border border-slate-600">
                            <h4 class="text-sm font-medium text-white">Advanced ECG Analysis</h4>
                            <p class="text-xs text-text-secondary mt-1">Based on your ECG mastery</p>
                        </div>
                        <div class="p-3 bg-surface-light/50 rounded-lg border border-slate-600">
                            <h4 class="text-sm font-medium text-white">Signal Processing Lab</h4>
                            <p class="text-xs text-text-secondary mt-1">Perfect next step for EEG</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Access Cards -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold text-white mb-6">Quick Access Simulations</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- ECG Simulation -->
                <div class="card hover:shadow-glow-secondary transition-all duration-300 cursor-pointer group">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="ECG Waveform" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-2 left-2">
                            <i class="fas fa-heartbeat text-secondary text-xl"></i>
                        </div>
                    </div>
                    <h4 class="font-semibold text-white mb-2">ECG Simulator</h4>
                    <p class="text-sm text-text-secondary">Interactive electrocardiogram analysis</p>
                </div>

                <!-- EEG Simulation -->
                <div class="card hover:shadow-glow-primary transition-all duration-300 cursor-pointer group">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pexels.com/photos/8376277/pexels-photo-8376277.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="EEG Brainwaves" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-2 left-2">
                            <i class="fas fa-brain text-primary-400 text-xl"></i>
                        </div>
                    </div>
                    <h4 class="font-semibold text-white mb-2">EEG Analyzer</h4>
                    <p class="text-sm text-text-secondary">Brain signal pattern recognition</p>
                </div>

                <!-- Circuit Designer -->
                <div class="card hover:shadow-glow-accent transition-all duration-300 cursor-pointer group">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pixabay.com/photo/2017/08/10/08/47/laptop-2619463_1280.jpg" alt="Circuit Design" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-2 left-2">
                            <i class="fas fa-microchip text-accent-400 text-xl"></i>
                        </div>
                    </div>
                    <h4 class="font-semibold text-white mb-2">Circuit Designer</h4>
                    <p class="text-sm text-text-secondary">Build and test amplifier circuits</p>
                </div>

                <!-- Blood Pressure Monitor -->
                <div class="card hover:shadow-glow-primary transition-all duration-300 cursor-pointer group">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pexels.com/photos/4386466/pexels-photo-4386466.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Blood Pressure Monitor" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-2 left-2">
                            <i class="fas fa-heartbeat text-error-500 text-xl"></i>
                        </div>
                    </div>
                    <h4 class="font-semibold text-white mb-2">BP Monitor</h4>
                    <p class="text-sm text-text-secondary">Blood pressure measurement simulation</p>
                </div>
            </div>
        </section>

        <!-- Performance Analytics -->
        <section>
            <h3 class="text-xl font-semibold text-white mb-6">Performance Analytics</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Mastery Levels -->
                <div class="card">
                    <h4 class="text-lg font-semibold text-white mb-4">Mastery Levels</h4>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-text-secondary">ECG Analysis</span>
                                <span class="text-sm font-medium text-secondary">Expert</span>
                            </div>
                            <div class="w-full bg-surface-light rounded-full h-2">
                                <div class="bg-secondary h-2 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-text-secondary">EEG Processing</span>
                                <span class="text-sm font-medium text-primary-400">Intermediate</span>
                            </div>
                            <div class="w-full bg-surface-light rounded-full h-2">
                                <div class="bg-primary h-2 rounded-full" style="width: 65%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-text-secondary">Circuit Design</span>
                                <span class="text-sm font-medium text-accent-400">Beginner</span>
                            </div>
                            <div class="w-full bg-surface-light rounded-full h-2">
                                <div class="bg-accent h-2 rounded-full" style="width: 30%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Learning Statistics -->
                <div class="card">
                    <h4 class="text-lg font-semibold text-white mb-4">Learning Statistics</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center p-4 bg-surface-light/50 rounded-lg">
                            <div class="text-2xl font-bold text-primary-400">24</div>
                            <div class="text-sm text-text-secondary">Modules Completed</div>
                        </div>
                        <div class="text-center p-4 bg-surface-light/50 rounded-lg">
                            <div class="text-2xl font-bold text-secondary">156</div>
                            <div class="text-sm text-text-secondary">Hours Studied</div>
                        </div>
                        <div class="text-center p-4 bg-surface-light/50 rounded-lg">
                            <div class="text-2xl font-bold text-accent-400">8</div>
                            <div class="text-sm text-text-secondary">Certificates Earned</div>
                        </div>
                        <div class="text-center p-4 bg-surface-light/50 rounded-lg">
                            <div class="text-2xl font-bold text-error-500">3</div>
                            <div class="text-sm text-text-secondary">Circuits Built</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Floating Action Button -->
    <div class="fixed bottom-6 right-6 z-50">
        <button class="w-14 h-14 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full shadow-dramatic hover:shadow-glow-primary transition-all duration-300 flex items-center justify-center group">
            <i class="fas fa-play text-white text-lg group-hover:scale-110 transition-transform duration-300"></i>
        </button>
    </div>

    <!-- JavaScript for Interactivity -->
    <script>
        // Language Toggle Functionality
        const langEn = document.getElementById('langEn');
        const langAr = document.getElementById('langAr');
        const htmlElement = document.documentElement;

        langEn.addEventListener('click', () => {
            htmlElement.setAttribute('lang', 'en');
            htmlElement.setAttribute('dir', 'ltr');
            langEn.classList.add('bg-primary', 'text-white');
            langEn.classList.remove('text-text-secondary');
            langAr.classList.remove('bg-primary', 'text-white');
            langAr.classList.add('text-text-secondary');
        });

        langAr.addEventListener('click', () => {
            htmlElement.setAttribute('lang', 'ar');
            htmlElement.setAttribute('dir', 'rtl');
            langAr.classList.add('bg-primary', 'text-white');
            langAr.classList.remove('text-text-secondary');
            langEn.classList.remove('bg-primary', 'text-white');
            langEn.classList.add('text-text-secondary');
        });

        // Quick Access Card Interactions
        document.querySelectorAll('.card.cursor-pointer').forEach(card => {
            card.addEventListener('click', () => {
                const title = card.querySelector('h4').textContent;
                if (title.includes('ECG')) {
                    window.location.href = 'simulation_laboratory.html';
                } else if (title.includes('Circuit')) {
                    window.location.href = 'circuit_design_workbench.html';
                } else {
                    window.location.href = 'simulation_laboratory.html';
                }
            });
        });

        // Floating Action Button
        document.querySelector('.fixed button').addEventListener('click', () => {
            window.location.href = 'simulation_laboratory.html';
        });

        // Animate progress bars on load
        window.addEventListener('load', () => {
            const progressBars = document.querySelectorAll('[style*="width:"]');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                    bar.style.transition = 'width 1s ease-out';
                }, 500);
            });
        });
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
</body>
</html>